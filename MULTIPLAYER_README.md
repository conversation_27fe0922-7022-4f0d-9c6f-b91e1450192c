# Multiplayer Flag Guessing Game

This document describes the multiplayer functionality added to the flag guessing game.

## Overview

The multiplayer system allows multiple players to compete in real-time flag guessing games. Players can create rooms, join existing rooms, and play together with synchronized questions and scoring.

## Architecture

### Frontend Components

- **SocketProvider**: React context provider for WebSocket connection management
- **SocketContext**: React context for accessing socket state across components
- **MultiplayerPage**: Main multiplayer interface with room creation/joining
- **RoomLobby**: Room waiting area showing players and game settings
- **MultiplayerGame**: Real-time game interface with synchronized questions
- **CreateRoomDialog**: Modal for creating new game rooms
- **JoinRoomDialog**: Modal for joining existing rooms

### Backend Infrastructure

- **WebSocket Server** (`websocket-server.ts`): Handles real-time communication
- **Room Management** (`lib/utils/room-management.ts`): Game room lifecycle and state
- **Socket Actions** (`lib/constants/socketActions.ts`): Message type constants
- **Type Definitions** (`lib/types/multiplayer.ts`): TypeScript interfaces

## Features

### Room Management
- Create rooms with custom names and settings
- Join rooms by room ID
- Host controls for starting games
- Player management with host privileges
- Room capacity limits (2-8 players)

### Game Flow
- Synchronized question delivery
- Real-time answer submission
- Live scoring and leaderboards
- Round-by-round progression
- Game completion with final rankings

### Player Features
- Unique player colors
- Session-based reconnection
- Real-time player status updates
- Chat-ready infrastructure (not implemented)

## Getting Started

### 1. Start the WebSocket Server
```bash
pnpm run dev:ws-server
```
The server will start on port 3001 by default.

### 2. Start the Next.js Application
```bash
pnpm run dev
```
The web app will be available at http://localhost:3000

### 3. Access Multiplayer
- Navigate to http://localhost:3000/multiplayer
- Create a room or join an existing one
- Invite friends using the room ID
- Start playing when ready!

## Technical Details

### WebSocket Communication
- Native WebSocket API (not Socket.IO)
- JSON message format
- Automatic reconnection handling
- Session token-based player identification

### Game Logic Integration
- Uses existing `generateQuestion` function
- Supports all difficulty levels (easy, medium, hard, expert)
- Maintains game state consistency across players
- Handles player disconnections gracefully

### State Management
- React Context for client-side state
- EventEmitter pattern for server-side room management
- Real-time synchronization of game state
- Optimistic UI updates with server confirmation

## Environment Variables

- `WS_PORT`: WebSocket server port (default: 3001)

## Development Notes

The multiplayer system is designed to be:
- **Scalable**: Room-based architecture supports multiple concurrent games
- **Resilient**: Handles player disconnections and reconnections
- **Extensible**: Easy to add features like chat, spectator mode, etc.
- **Type-safe**: Full TypeScript coverage for all multiplayer code

## Future Enhancements

- Chat system during games
- Spectator mode
- Tournament brackets
- Player statistics and rankings
- Custom game modes (time limits, bonus rounds)
- Mobile app support
