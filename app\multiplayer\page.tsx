'use client';

import { useEffect, useState } from 'react';
import { nanoid } from 'nanoid';
import { SocketProvider } from '@/lib/context/SocketProvider';
import { MultiplayerPage } from '@/components/multiplayer/MultiplayerPage';

export default function MultiplayerPageRoute() {
  const [sessionToken, setSessionToken] = useState<string | null>(null);

  useEffect(() => {
    // Generate or retrieve session token
    let token = localStorage.getItem('sessionToken');
    if (!token) {
      token = nanoid();
      localStorage.setItem('sessionToken', token);
    }
    setSessionToken(token);
  }, []);

  if (!sessionToken) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-2 text-muted-foreground">Initializing...</p>
        </div>
      </div>
    );
  }

  return (
    <SocketProvider sessionToken={sessionToken}>
      <MultiplayerPage />
    </SocketProvider>
  );
}
