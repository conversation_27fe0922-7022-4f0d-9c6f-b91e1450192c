'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useSocket } from '@/lib/context/SocketContext';
import { CREATE_ROOM } from '@/lib/constants/socketActions';
import { Difficulty, DIFFICULTY_LEVELS } from '@/lib/constants';
import { Users, Plus } from 'lucide-react';

interface CreateRoomDialogProps {
  trigger?: React.ReactNode;
}

export const CreateRoomDialog: React.FC<CreateRoomDialogProps> = ({ trigger }) => {
  const [open, setOpen] = useState(false);
  const [roomName, setRoomName] = useState('');
  const [username, setUsername] = useState('');
  const [difficulty, setDifficulty] = useState<Difficulty>('medium');
  const [maxPlayers, setMaxPlayers] = useState(4);
  const [isCreating, setIsCreating] = useState(false);
  
  const { socket, isConnected } = useSocket();

  const handleCreateRoom = async () => {
    if (!socket || !isConnected || !roomName.trim() || !username.trim()) {
      return;
    }

    setIsCreating(true);

    try {
      socket.send(JSON.stringify({
        type: CREATE_ROOM,
        name: roomName.trim(),
        username: username.trim(),
        difficulty,
        maxPlayers,
      }));

      // Close dialog on successful creation
      setOpen(false);
      setRoomName('');
      setUsername('');
    } catch (error) {
      console.error('Error creating room:', error);
    } finally {
      setIsCreating(false);
    }
  };

  const defaultTrigger = (
    <Button className="w-full" size="lg">
      <Plus className="w-4 h-4 mr-2" />
      Create Room
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Create Multiplayer Room
          </DialogTitle>
          <DialogDescription>
            Create a new room to play flag guessing with friends.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="username">Your Name</Label>
            <Input
              id="username"
              placeholder="Enter your name"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              maxLength={80}
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="room-name">Room Name</Label>
            <Input
              id="room-name"
              placeholder="Enter room name"
              value={roomName}
              onChange={(e) => setRoomName(e.target.value)}
              maxLength={80}
            />
          </div>

          <div className="grid gap-2">
            <Label htmlFor="difficulty">Difficulty</Label>
            <Select value={difficulty} onValueChange={(value: Difficulty) => setDifficulty(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                {DIFFICULTY_LEVELS.map((level) => (
                  <SelectItem key={level} value={level}>
                    {level.charAt(0).toUpperCase() + level.slice(1)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="grid gap-2">
            <Label htmlFor="max-players">Max Players</Label>
            <Select value={maxPlayers.toString()} onValueChange={(value) => setMaxPlayers(parseInt(value))}>
              <SelectTrigger>
                <SelectValue placeholder="Select max players" />
              </SelectTrigger>
              <SelectContent>
                {[2, 3, 4, 5, 6, 7, 8].map((num) => (
                  <SelectItem key={num} value={num.toString()}>
                    {num} players
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>

        <DialogFooter>
          <Button 
            type="submit" 
            onClick={handleCreateRoom}
            disabled={!roomName.trim() || !username.trim() || isCreating || !isConnected}
            className="w-full"
          >
            {isCreating ? 'Creating...' : 'Create Room'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
