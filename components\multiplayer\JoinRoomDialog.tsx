'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { useSocket } from '@/lib/context/SocketContext';
import { JOIN_ROOM, CHECK_IF_ROOM_EXISTS } from '@/lib/constants/socketActions';
import { UserPlus, Dice6 } from 'lucide-react';

interface JoinRoomDialogProps {
  trigger?: React.ReactNode;
}

export const JoinRoomDialog: React.FC<JoinRoomDialogProps> = ({ trigger }) => {
  const [open, setOpen] = useState(false);
  const [roomId, setRoomId] = useState('');
  const [username, setUsername] = useState('');
  const [isJoining, setIsJoining] = useState(false);
  const [error, setError] = useState('');
  
  const { socket, isConnected } = useSocket();

  const generateRandomRoomId = () => {
    // Generate a random 6-character room ID for testing
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 6; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    setRoomId(result);
  };

  const handleJoinRoom = async () => {
    if (!socket || !isConnected || !roomId.trim() || !username.trim()) {
      return;
    }

    setIsJoining(true);
    setError('');

    try {
      // First check if room exists
      socket.send(JSON.stringify({
        type: CHECK_IF_ROOM_EXISTS,
        roomId: roomId.trim().toUpperCase(),
      }));

      // Listen for response (this would be better handled with promises/callbacks)
      const checkTimeout = setTimeout(() => {
        // If no response, try to join anyway
        socket.send(JSON.stringify({
          type: JOIN_ROOM,
          roomId: roomId.trim().toUpperCase(),
          username: username.trim(),
        }));
      }, 1000);

      // For now, just attempt to join
      socket.send(JSON.stringify({
        type: JOIN_ROOM,
        roomId: roomId.trim().toUpperCase(),
        username: username.trim(),
      }));

      // Close dialog on successful join attempt
      setOpen(false);
      setRoomId('');
      setUsername('');
      setError('');
    } catch (error) {
      console.error('Error joining room:', error);
      setError('Failed to join room. Please try again.');
    } finally {
      setIsJoining(false);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" className="w-full" size="lg">
      <UserPlus className="w-4 h-4 mr-2" />
      Join Room
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || defaultTrigger}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <UserPlus className="w-5 h-5" />
            Join Multiplayer Room
          </DialogTitle>
          <DialogDescription>
            Enter a room ID to join an existing game.
          </DialogDescription>
        </DialogHeader>
        
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="username">Your Name</Label>
            <Input
              id="username"
              placeholder="Enter your name"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              maxLength={80}
            />
          </div>
          
          <div className="grid gap-2">
            <Label htmlFor="room-id">Room ID</Label>
            <div className="flex gap-2">
              <Input
                id="room-id"
                placeholder="Enter 6-character room ID"
                value={roomId}
                onChange={(e) => setRoomId(e.target.value.toUpperCase())}
                maxLength={6}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={generateRandomRoomId}
                title="Generate random room ID (for testing)"
              >
                <Dice6 className="w-4 h-4" />
              </Button>
            </div>
            {roomId && roomId.length !== 6 && (
              <p className="text-sm text-muted-foreground">
                Room ID must be exactly 6 characters
              </p>
            )}
          </div>

          {error && (
            <div className="text-sm text-red-500 bg-red-50 p-2 rounded">
              {error}
            </div>
          )}
        </div>

        <DialogFooter>
          <Button 
            type="submit" 
            onClick={handleJoinRoom}
            disabled={!roomId.trim() || roomId.length !== 6 || !username.trim() || isJoining || !isConnected}
            className="w-full"
          >
            {isJoining ? 'Joining...' : 'Join Room'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
