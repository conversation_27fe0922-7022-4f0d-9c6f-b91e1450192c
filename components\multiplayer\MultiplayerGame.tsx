'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useSocket } from '@/lib/context/SocketContext';
import { SUBMIT_ANSWER, NEXT_QUESTION } from '@/lib/constants/socketActions';
import { Crown, Clock, Users, Trophy } from 'lucide-react';
import { Player } from '@/lib/types/multiplayer';
import Image from 'next/image';

interface MultiplayerGameProps {
  onGameEnd?: () => void;
}

export const MultiplayerGame: React.FC<MultiplayerGameProps> = ({ onGameEnd }) => {
  const { socket, room, player, isConnected } = useSocket();
  const [selectedAnswer, setSelectedAnswer] = useState<string>('');
  const [timeLeft, setTimeLeft] = useState<number>(30);
  const [hasAnswered, setHasAnswered] = useState(false);

  useEffect(() => {
    if (!room?.currentQuestion) return;

    const startTime = room.currentQuestion.startTime || Date.now();
    const timeLimit = (room.currentQuestion.timeLimit || 30) * 1000;
    
    const timer = setInterval(() => {
      const elapsed = Date.now() - startTime;
      const remaining = Math.max(0, Math.ceil((timeLimit - elapsed) / 1000));
      setTimeLeft(remaining);
      
      if (remaining === 0) {
        clearInterval(timer);
        // Auto-submit if time runs out
        if (!hasAnswered && socket && isConnected) {
          handleSubmitAnswer('');
        }
      }
    }, 1000);

    return () => clearInterval(timer);
  }, [room?.currentQuestion, hasAnswered, socket, isConnected]);

  const handleSubmitAnswer = (answer: string) => {
    if (!socket || !isConnected || hasAnswered || !room?.currentQuestion) return;
    
    setHasAnswered(true);
    setSelectedAnswer(answer);
    
    const isCorrect = answer === room.currentQuestion.currentCountry.name;
    
    socket.send(JSON.stringify({
      type: SUBMIT_ANSWER,
      answer,
      isCorrect,
      answerTime: Date.now() - (room.currentQuestion.startTime || Date.now()),
    }));
  };

  const handleNextQuestion = () => {
    if (!socket || !isConnected || !player?.isHost) return;
    
    socket.send(JSON.stringify({
      type: NEXT_QUESTION,
    }));
    
    setHasAnswered(false);
    setSelectedAnswer('');
  };

  if (!room || !player) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">Loading game...</p>
      </div>
    );
  }

  const players = Array.from(room.players.values()).sort((a, b) => b.score - a.score);
  const currentQuestion = room.currentQuestion;

  // Game finished view
  if (room.gameState === 'finished') {
    return (
      <div className="max-w-4xl mx-auto space-y-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-center">
              <Trophy className="w-6 h-6 text-yellow-500" />
              Game Finished!
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">Final Leaderboard</h3>
              {players.map((p, index) => (
                <div
                  key={p.id}
                  className={`flex items-center justify-between p-4 rounded-lg border ${
                    index === 0 ? 'bg-yellow-50 border-yellow-200' :
                    index === 1 ? 'bg-gray-50 border-gray-200' :
                    index === 2 ? 'bg-orange-50 border-orange-200' : 'bg-white'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <div className="text-2xl font-bold text-muted-foreground">
                      #{index + 1}
                    </div>
                    <div
                      className="w-10 h-10 rounded-full flex items-center justify-center text-white font-medium"
                      style={{ backgroundColor: p.color || '#6366f1' }}
                    >
                      {p.name.charAt(0).toUpperCase()}
                    </div>
                    <div>
                      <p className="font-medium">{p.name}</p>
                      {p.isHost && (
                        <Badge variant="outline" className="text-xs">
                          <Crown className="w-3 h-3 mr-1" />
                          Host
                        </Badge>
                      )}
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-2xl font-bold">{p.score}</p>
                    <p className="text-sm text-muted-foreground">points</p>
                  </div>
                </div>
              ))}
            </div>

            <div className="mt-6 flex gap-3">
              {player.isHost && (
                <Button onClick={() => onGameEnd?.()} className="flex-1">
                  Back to Lobby
                </Button>
              )}
              <Button variant="outline" onClick={() => window.location.href = '/'}>
                Single Player
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Active game view
  if (!currentQuestion) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">Waiting for next question...</p>
      </div>
    );
  }

  const progressPercentage = (timeLeft / (room.settings.timeLimit || 30)) * 100;

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* Game Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <h1 className="text-2xl font-bold">Round {room.currentRound} of {room.totalRounds}</h1>
          <Badge variant="secondary">
            Question {currentQuestion.questionNumber}
          </Badge>
        </div>
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            <span className={`font-mono text-lg ${timeLeft <= 5 ? 'text-red-500' : ''}`}>
              {timeLeft}s
            </span>
          </div>
          <Progress value={progressPercentage} className="w-24" />
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Game Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Flag Display */}
          <Card>
            <CardContent className="pt-6">
              <div className="text-center space-y-4">
                <h2 className="text-xl font-semibold">Which country does this flag belong to?</h2>
                <div className="flex justify-center">
                  <div className="relative w-64 h-40 border-2 border-gray-200 rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src={currentQuestion.currentCountry.flag}
                      alt="Flag to guess"
                      fill
                      className="object-cover"
                      priority
                    />
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Answer Options */}
          <Card>
            <CardContent className="pt-6">
              <div className="grid grid-cols-2 gap-3">
                {currentQuestion.options.map((country) => (
                  <Button
                    key={country.name}
                    variant={selectedAnswer === country.name ? "default" : "outline"}
                    className="h-12 text-left justify-start"
                    onClick={() => handleSubmitAnswer(country.name)}
                    disabled={hasAnswered || timeLeft === 0}
                  >
                    {country.name}
                  </Button>
                ))}
              </div>

              {hasAnswered && (
                <div className="mt-4 p-3 rounded-lg bg-muted">
                  <p className="text-sm text-muted-foreground">
                    Answer submitted! Waiting for other players...
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Host Controls */}
          {player.isHost && (
            <Card>
              <CardContent className="pt-6">
                <Button
                  onClick={handleNextQuestion}
                  disabled={!isConnected}
                  className="w-full"
                >
                  Next Question
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar - Players */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="w-5 h-5" />
                Leaderboard
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {players.map((p, index) => (
                  <div
                    key={p.id}
                    className={`flex items-center justify-between p-3 rounded-lg border ${
                      p.id === player.id ? 'bg-blue-50 border-blue-200' : 'bg-white'
                    }`}
                  >
                    <div className="flex items-center gap-3">
                      <div className="text-sm font-bold text-muted-foreground">
                        #{index + 1}
                      </div>
                      <div
                        className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                        style={{ backgroundColor: p.color || '#6366f1' }}
                      >
                        {p.name.charAt(0).toUpperCase()}
                      </div>
                      <div>
                        <p className="font-medium text-sm">{p.name}</p>
                        <div className="flex items-center gap-1">
                          {p.isHost && <Crown className="w-3 h-3 text-yellow-500" />}
                          {p.hasAnswered && (
                            <Badge variant="secondary" className="text-xs">
                              Answered
                            </Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold">{p.score}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};
