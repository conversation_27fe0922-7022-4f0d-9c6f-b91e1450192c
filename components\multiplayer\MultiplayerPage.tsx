'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { useSocket } from '@/lib/context/SocketContext';
import { CreateRoomDialog } from './CreateRoomDialog';
import { JoinRoomDialog } from './JoinRoomDialog';
import { RoomLobby } from './RoomLobby';
import { MultiplayerGame } from './MultiplayerGame';
import { Users, Wifi, WifiOff, AlertCircle } from 'lucide-react';

export const MultiplayerPage: React.FC = () => {
  const { room, player, isConnected, isConnecting, error } = useSocket();
  const [currentView, setCurrentView] = useState<'menu' | 'lobby' | 'game'>('menu');

  useEffect(() => {
    if (room && player) {
      if (room.gameState === 'waiting') {
        setCurrentView('lobby');
      } else if (room.gameState === 'playing' || room.gameState === 'starting') {
        setCurrentView('game');
      } else if (room.gameState === 'finished') {
        setCurrentView('lobby'); // Show results in lobby
      }
    } else {
      setCurrentView('menu');
    }
  }, [room, player]);

  const handleLeaveRoom = () => {
    setCurrentView('menu');
  };

  // Connection status indicator
  const ConnectionStatus = () => (
    <div className="flex items-center gap-2 text-sm">
      {isConnecting ? (
        <>
          <div className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse" />
          <span className="text-yellow-600">Connecting...</span>
        </>
      ) : isConnected ? (
        <>
          <Wifi className="w-4 h-4 text-green-500" />
          <span className="text-green-600">Connected</span>
        </>
      ) : (
        <>
          <WifiOff className="w-4 h-4 text-red-500" />
          <span className="text-red-600">Disconnected</span>
        </>
      )}
    </div>
  );

  // Error display
  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-md mx-auto pt-20">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-red-600">
                <AlertCircle className="w-5 h-5" />
                Connection Error
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground mb-4">{error}</p>
              <Button 
                onClick={() => window.location.reload()} 
                className="w-full"
              >
                Retry Connection
              </Button>
            </CardContent>
          </Card>
        </div>
      </div>
    );
  }

  // Main menu view
  if (currentView === 'menu') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-md mx-auto pt-20 space-y-6">
          {/* Header */}
          <div className="text-center space-y-2">
            <div className="flex items-center justify-center gap-2 text-3xl font-bold text-gray-800">
              <Users className="w-8 h-8" />
              Multiplayer
            </div>
            <p className="text-muted-foreground">
              Play flag guessing with friends!
            </p>
            <ConnectionStatus />
          </div>

          {/* Menu Options */}
          <Card>
            <CardHeader>
              <CardTitle>Join or Create a Game</CardTitle>
              <CardDescription>
                Create a new room or join an existing one with friends.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <CreateRoomDialog />
              <JoinRoomDialog />
            </CardContent>
          </Card>

          {/* Back to Single Player */}
          <div className="text-center">
            <Button 
              variant="ghost" 
              onClick={() => window.location.href = '/'}
              className="text-muted-foreground"
            >
              ← Back to Single Player
            </Button>
          </div>
        </div>
      </div>
    );
  }

  // Lobby view
  if (currentView === 'lobby') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-4xl mx-auto pt-8 space-y-6">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Users className="w-6 h-6" />
              <h1 className="text-2xl font-bold">Game Lobby</h1>
            </div>
            <ConnectionStatus />
          </div>

          <RoomLobby onLeaveRoom={handleLeaveRoom} />
        </div>
      </div>
    );
  }

  // Game view
  if (currentView === 'game') {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
        <div className="max-w-6xl mx-auto pt-4">
          <MultiplayerGame onGameEnd={handleLeaveRoom} />
        </div>
      </div>
    );
  }

  return null;
};
