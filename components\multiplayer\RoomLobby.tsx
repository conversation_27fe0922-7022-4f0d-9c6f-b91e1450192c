'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useSocket } from '@/lib/context/SocketContext';
import { START_GAME, LEAVE_ROOM } from '@/lib/constants/socketActions';
import { Crown, Users, Copy, Play, LogOut } from 'lucide-react';
import { Player } from '@/lib/types/multiplayer';

interface RoomLobbyProps {
  onLeaveRoom?: () => void;
}

export const RoomLobby: React.FC<RoomLobbyProps> = ({ onLeaveRoom }) => {
  const { socket, room, player, isConnected } = useSocket();

  if (!room || !player) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <p className="text-muted-foreground">Loading room...</p>
      </div>
    );
  }

  const handleStartGame = () => {
    if (!socket || !isConnected || !player.isHost) return;
    
    socket.send(JSON.stringify({
      type: START_GAME,
    }));
  };

  const handleLeaveRoom = () => {
    if (!socket || !isConnected) return;
    
    socket.send(JSON.stringify({
      type: LEAVE_ROOM,
    }));
    
    onLeaveRoom?.();
  };

  const copyInviteCode = async () => {
    if (!room.inviteCode) return;
    
    try {
      await navigator.clipboard.writeText(room.inviteCode);
      // You might want to show a toast notification here
    } catch (err) {
      console.error('Failed to copy invite code:', err);
    }
  };

  const players = Array.from(room.players.values());
  const canStartGame = player.isHost && players.length >= 2;

  return (
    <div className="max-w-2xl mx-auto space-y-6">
      {/* Room Info */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>{room.name}</span>
            <Badge variant="secondary">
              {players.length}/{room.maxPlayers} players
            </Badge>
          </CardTitle>
          <CardDescription>
            Difficulty: {room.difficulty.charAt(0).toUpperCase() + room.difficulty.slice(1)}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Room ID:</span>
              <code className="px-2 py-1 bg-muted rounded text-sm font-mono">
                {room.id}
              </code>
            </div>
            {room.inviteCode && (
              <Button
                variant="outline"
                size="sm"
                onClick={copyInviteCode}
                className="flex items-center gap-2"
              >
                <Copy className="w-3 h-3" />
                Copy Invite
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Players List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Users className="w-5 h-5" />
            Players
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            {players.map((p) => (
              <div
                key={p.id}
                className="flex items-center justify-between p-3 rounded-lg border"
              >
                <div className="flex items-center gap-3">
                  <div
                    className="w-8 h-8 rounded-full flex items-center justify-center text-white text-sm font-medium"
                    style={{ backgroundColor: p.color || '#6366f1' }}
                  >
                    {p.name.charAt(0).toUpperCase()}
                  </div>
                  <div>
                    <p className="font-medium">{p.name}</p>
                    {p.id === player.id && (
                      <p className="text-xs text-muted-foreground">You</p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  {p.isHost && (
                    <Badge variant="default" className="flex items-center gap-1">
                      <Crown className="w-3 h-3" />
                      Host
                    </Badge>
                  )}
                  <span className="text-sm text-muted-foreground">
                    Score: {p.score}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Game Controls */}
      <Card>
        <CardContent className="pt-6">
          <div className="flex gap-3">
            {player.isHost ? (
              <Button
                onClick={handleStartGame}
                disabled={!canStartGame || !isConnected}
                className="flex-1 flex items-center gap-2"
                size="lg"
              >
                <Play className="w-4 h-4" />
                {canStartGame ? 'Start Game' : `Need ${2 - players.length} more player${2 - players.length !== 1 ? 's' : ''}`}
              </Button>
            ) : (
              <div className="flex-1 text-center py-3 text-muted-foreground">
                Waiting for host to start the game...
              </div>
            )}
            
            <Button
              variant="outline"
              onClick={handleLeaveRoom}
              disabled={!isConnected}
              className="flex items-center gap-2"
            >
              <LogOut className="w-4 h-4" />
              Leave
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Game Status */}
      {room.gameState !== 'waiting' && (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center">
              <p className="text-lg font-medium">
                Game Status: {room.gameState.replace('_', ' ').toUpperCase()}
              </p>
              {room.currentRound > 0 && (
                <p className="text-muted-foreground">
                  Round {room.currentRound} of {room.totalRounds}
                </p>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};
