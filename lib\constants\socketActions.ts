// Socket action constants for multiplayer flag guessing game
export const CREATE_ROOM = "CREATE_ROOM";
export const JOIN_ROOM = "JOIN_ROOM";
export const LEAVE_ROOM = "LEAVE_ROOM";
export const CHECK_IF_ROOM_EXISTS = "CHECK_IF_ROOM_EXISTS";
export const GET_ROOM_INFO = "GET_ROOM_INFO";
export const START_GAME = "START_GAME";
export const SUBMIT_ANSWER = "SUBMIT_ANSWER";
export const NEXT_QUESTION = "NEXT_QUESTION";
export const GAME_FINISHED = "GAME_FINISHED";
export const USER_JOINED = "USER_JOINED";
export const USER_LEFT = "USER_LEFT";
export const SET_HOST = "SET_HOST";
export const KICK_USER = "KICK_USER";
export const PING = "PING";
export const PONG = "PONG";
export const RECONNECT_USER = "RECONNECT_USER";
export const ERROR = "ERROR";
export const SERVER_MESSAGE = "SERVER_MESSAGE";
export const USER_MESSAGE = "USER_MESSAGE";
export const ROOM_SETTINGS_CHANGED = "ROOM_SETTINGS_CHANGED";
export const PLAYER_SCORE_UPDATED = "PLAYER_SCORE_UPDATED";
export const QUESTION_REVEALED = "QUESTION_REVEALED";
export const ANSWER_SUBMITTED = "ANSWER_SUBMITTED";
export const ROUND_ENDED = "ROUND_ENDED";
export const GAME_STATE_CHANGED = "GAME_STATE_CHANGED";
export const JOIN_ROOM_BY_INVITE = "JOIN_ROOM_BY_INVITE";
