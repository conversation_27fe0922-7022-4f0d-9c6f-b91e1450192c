'use client';

import React from 'react';
import { <PERSON><PERSON><PERSON>, Player } from '@/lib/types/multiplayer';

export interface SocketContextType {
  socket: WebSocket | null;
  sessionToken: string | null;
  room: GameRoom | null;
  player: Player | null;
  isConnecting: boolean;
  isConnected: boolean;
  error: string | null;
}

export const SocketContext = React.createContext<SocketContextType>({
  socket: null,
  sessionToken: null,
  room: null,
  player: null,
  isConnecting: false,
  isConnected: false,
  error: null,
});

export const useSocket = () => {
  const context = React.useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};
