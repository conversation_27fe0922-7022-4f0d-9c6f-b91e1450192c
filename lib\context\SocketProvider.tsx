'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { SocketContext } from './SocketContext';
import { GameRoom, Player } from '@/lib/types/multiplayer';
import {
  GET_ROOM_INFO,
  ERROR,
  USER_JOINED,
  USER_LEFT,
  GAME_STATE_CHANGED,
  PLAYER_SCORE_UPDATED
} from '@/lib/constants/socketActions';

interface SocketProviderProps {
  children: React.ReactNode;
  sessionToken: string | null;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({
  children,
  sessionToken,
}) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [room, setRoom] = useState<GameRoom | null>(null);
  const [player, setPlayer] = useState<Player | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const handleMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'init_success':
        console.log('WebSocket initialization successful');
        break;

      case GET_ROOM_INFO:
        if (data.room) {
          // Convert players array back to Map if needed
          const roomData = { ...data.room };
          if (Array.isArray(roomData.players)) {
            roomData.players = new Map(roomData.players);
          }
          setRoom(roomData);

          // Find current player
          const currentPlayer = Array.from(roomData.players.values()).find(
            (p: any) => p.socketId === sessionToken
          ) as Player | undefined;
          if (currentPlayer) {
            setPlayer(currentPlayer);
          }
        }
        break;

      case USER_JOINED:
        if (data.room) {
          const roomData = { ...data.room };
          if (Array.isArray(roomData.players)) {
            roomData.players = new Map(roomData.players);
          }
          setRoom(roomData);
        }
        break;

      case USER_LEFT:
        if (data.room) {
          const roomData = { ...data.room };
          if (Array.isArray(roomData.players)) {
            roomData.players = new Map(roomData.players);
          }
          setRoom(roomData);
        }
        break;

      case GAME_STATE_CHANGED:
        if (data.room) {
          const roomData = { ...data.room };
          if (Array.isArray(roomData.players)) {
            roomData.players = new Map(roomData.players);
          }
          setRoom(roomData);
        }
        break;

      case PLAYER_SCORE_UPDATED:
        setRoom(prevRoom => {
          if (!prevRoom) return prevRoom;
          const updatedRoom = { ...prevRoom };
          const player = updatedRoom.players.get(data.playerId);
          if (player) {
            player.score = data.score;
            player.hasAnswered = data.hasAnswered;
          }
          return updatedRoom;
        });
        break;

      case ERROR:
        console.error('Server error:', data.message);
        setError(data.message);
        break;

      default:
        console.log('Unhandled message type:', data.type);
    }
  }, [sessionToken]);

  const connect = useCallback(() => {
    if (!sessionToken) {
      setError('No session token provided');
      return;
    }

    setIsConnecting(true);
    setError(null);

    const wsUrl = process.env.NODE_ENV === 'production'
      ? `wss://${window.location.host}/ws`
      : 'ws://localhost:3001';

    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setIsConnecting(false);
      setError(null);
      reconnectAttemptsRef.current = 0;

      // Send initialization message
      ws.send(JSON.stringify({
        type: 'init',
        sessionToken
      }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleMessage(data);
      } catch (err) {
        console.error('Error parsing WebSocket message:', err);
      }
    };

    ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);
      setSocket(null);

      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
        console.log(`Attempting to reconnect in ${delay}ms...`);

        reconnectTimeoutRef.current = setTimeout(() => {
          reconnectAttemptsRef.current++;
          connect();
        }, delay);
      } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
        setError('Failed to reconnect after multiple attempts');
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setError('Connection error occurred');
      setIsConnecting(false);
    };

    setSocket(ws);
  }, [sessionToken, handleMessage]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socket) {
      socket.close(1000, 'User disconnected');
    }
    
    setSocket(null);
    setIsConnected(false);
    setRoom(null);
    setPlayer(null);
  }, [socket]);

  useEffect(() => {
    if (sessionToken) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [sessionToken]); // Remove connect and disconnect from dependencies

  const value = {
    socket,
    sessionToken,
    room,
    player,
    isConnecting,
    isConnected,
    error,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
