'use client';

import React, { useEffect, useState, useCallback, useRef } from 'react';
import { SocketContext } from './SocketContext';
import { GameRoom, Player } from '@/lib/types/multiplayer';
import { 
  GET_ROOM_INFO, 
  ERROR, 
  USER_JOINED, 
  USER_LEFT, 
  SERVER_MESSAGE,
  GAME_STATE_CHANGED,
  PLAYER_SCORE_UPDATED
} from '@/lib/constants/socketActions';

interface SocketProviderProps {
  children: React.ReactNode;
  sessionToken: string | null;
}

export const SocketProvider: React.FC<SocketProviderProps> = ({
  children,
  sessionToken,
}) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [room, setRoom] = useState<GameRoom | null>(null);
  const [player, setPlayer] = useState<Player | null>(null);
  const [isConnecting, setIsConnecting] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttemptsRef = useRef(0);
  const maxReconnectAttempts = 5;

  const connect = useCallback(() => {
    if (!sessionToken) {
      setError('No session token provided');
      return;
    }

    setIsConnecting(true);
    setError(null);

    const wsUrl = process.env.NODE_ENV === 'production'
      ? `wss://${window.location.host}/ws`
      : 'ws://localhost:3003';

    const ws = new WebSocket(wsUrl);

    ws.onopen = () => {
      console.log('WebSocket connected');
      setIsConnected(true);
      setIsConnecting(false);
      setError(null);
      reconnectAttemptsRef.current = 0;

      // Send initialization message
      ws.send(JSON.stringify({
        type: 'init',
        sessionToken
      }));
    };

    ws.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        handleMessage(data);
      } catch (err) {
        console.error('Error parsing WebSocket message:', err);
      }
    };

    ws.onclose = (event) => {
      console.log('WebSocket disconnected:', event.code, event.reason);
      setIsConnected(false);
      setSocket(null);

      // Attempt to reconnect if not a normal closure
      if (event.code !== 1000 && reconnectAttemptsRef.current < maxReconnectAttempts) {
        const delay = Math.min(1000 * Math.pow(2, reconnectAttemptsRef.current), 30000);
        console.log(`Attempting to reconnect in ${delay}ms...`);
        
        reconnectTimeoutRef.current = setTimeout(() => {
          reconnectAttemptsRef.current++;
          connect();
        }, delay);
      } else if (reconnectAttemptsRef.current >= maxReconnectAttempts) {
        setError('Failed to reconnect after multiple attempts');
      }
    };

    ws.onerror = (error) => {
      console.error('WebSocket error:', error);
      setError('Connection error occurred');
      setIsConnecting(false);
    };

    setSocket(ws);
  }, [sessionToken]);

  const handleMessage = useCallback((data: any) => {
    switch (data.type) {
      case 'init_success':
        console.log('WebSocket initialization successful');
        break;

      case GET_ROOM_INFO:
        if (data.room) {
          // Convert players array back to Map if needed
          const roomData = { ...data.room };
          if (Array.isArray(roomData.players)) {
            roomData.players = new Map(roomData.players.map((p: Player) => [p.id, p]));
          }
          setRoom(roomData);
        }
        break;

      case USER_JOINED:
        if (data.player) {
          console.log('User joined:', data.player.name);
        }
        break;

      case USER_LEFT:
        if (data.playerId) {
          console.log('User left:', data.playerId);
        }
        break;

      case SERVER_MESSAGE:
        console.log('Server message:', data.message);
        break;

      case GAME_STATE_CHANGED:
        if (data.gameState) {
          setRoom(prev => prev ? { ...prev, gameState: data.gameState } : null);
        }
        break;

      case PLAYER_SCORE_UPDATED:
        if (data.playerId && data.score !== undefined) {
          setRoom(prev => {
            if (!prev) return null;
            const updatedPlayers = new Map(prev.players);
            const player = updatedPlayers.get(data.playerId);
            if (player) {
              updatedPlayers.set(data.playerId, { ...player, score: data.score });
            }
            return { ...prev, players: updatedPlayers };
          });
        }
        break;

      case ERROR:
        console.error('Server error:', data.message);
        setError(data.message);
        break;

      default:
        console.log('Unhandled message type:', data.type);
    }
  }, []);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    
    if (socket) {
      socket.close(1000, 'User disconnected');
    }
    
    setSocket(null);
    setIsConnected(false);
    setRoom(null);
    setPlayer(null);
  }, [socket]);

  useEffect(() => {
    if (sessionToken) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [sessionToken, connect, disconnect]);

  const value = {
    socket,
    sessionToken,
    room,
    player,
    isConnecting,
    isConnected,
    error,
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
