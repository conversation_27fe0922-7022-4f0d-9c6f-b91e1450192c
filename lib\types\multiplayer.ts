import { Difficulty } from "@/lib/constants";
import { Country } from "@/lib/data/countries";

export type UserId = string;
export type RoomId = string;

export interface Player {
  id: UserId;
  name: string;
  score: number;
  isHost: boolean;
  isAdmin: boolean;
  socketId: string;
  roomId: RoomId;
  connectedAt: string;
  color?: string;
  hasAnswered?: boolean;
  lastAnswerTime?: number;
}

export interface GameQuestion {
  currentCountry: Country;
  options: Country[];
  questionNumber: number;
  timeLimit?: number;
  startTime?: number;
}

export type GameState = 'waiting' | 'starting' | 'playing' | 'round_ended' | 'finished';

export interface GameRoom {
  id: RoomId;
  name: string;
  host: UserId;
  inviteCode: string;
  players: Map<UserId, Player>;
  gameState: GameState;
  difficulty: Difficulty;
  maxPlayers: number;
  currentRound: number;
  totalRounds: number;
  currentQuestion?: GameQuestion;
  created: string;
  private: boolean;
  passcode?: string;
  previouslyConnectedMembers: { userId: UserId; username: string }[];
  settings: {
    timeLimit: number;
    pointsPerCorrect: number;
    pointsPerSpeed: number;
  };
}

export interface ChatMessage {
  id: string;
  userId: UserId;
  username: string;
  message: string;
  timestamp: string;
  type: 'USER' | 'SERVER';
  color?: string;
  isAdmin?: boolean;
}

export type Messages = ChatMessage[];

export enum ServerMessageType {
  USER_JOINED = 'USER_JOINED',
  USER_LEFT = 'USER_LEFT',
  USER_DISCONNECTED = 'USER_DISCONNECTED',
  USER_RECONNECTED = 'USER_RECONNECTED',
  NEW_HOST = 'NEW_HOST',
  GAME_STARTED = 'GAME_STARTED',
  GAME_ENDED = 'GAME_ENDED',
  ROUND_STARTED = 'ROUND_STARTED',
  ROUND_ENDED = 'ROUND_ENDED'
}

export interface ServerMessage {
  type: ServerMessageType;
  message: string;
  timestamp: string;
}

export interface AnswerSubmission {
  playerId: UserId;
  isCorrect: boolean;
  answerTime: number;
  selectedCountry: string;
}

export interface RoundResult {
  question: GameQuestion;
  answers: AnswerSubmission[];
  correctAnswer: string;
  leaderboard: Array<{
    playerId: UserId;
    playerName: string;
    score: number;
    roundScore: number;
  }>;
}

export interface GameResult {
  finalScores: Array<{
    playerId: UserId;
    playerName: string;
    score: number;
    rank: number;
  }>;
  totalRounds: number;
  gameId: string;
  duration: number;
}
