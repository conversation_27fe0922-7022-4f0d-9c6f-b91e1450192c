import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { <PERSON>R<PERSON>, Player, UserId, RoomId, GameState, GameQuestion } from '@/lib/types/multiplayer';
import { Difficulty } from '@/lib/constants';
import { generateQuestion } from './gameLogic';

export class RoomsSource extends EventEmitter {
  public rooms: Map<string, GameRoom> = new Map();

  constructor() {
    super();
  }

  clear(): void {
    this.rooms.clear();
    this.emit('rooms:cleared');
  }

  delete(id: string): void {
    const room = this.rooms.get(id);
    if (room) {
      this.rooms.delete(id);
      this.emit('room:deleted', room);
    }
  }

  get(id: string): GameRoom | undefined {
    return this.rooms.get(id);
  }

  has(id: string): boolean {
    return this.rooms.has(id);
  }

  set(id: string, room: GameRoom): void {
    const existingRoom = this.rooms.get(id);
    this.rooms.set(id, room);
    this.emit(existingRoom ? 'room:updated' : 'room:added', room);
  }

  getLength(): number {
    return Array.from(this.rooms.values()).length;
  }

  getAll(): Map<string, GameRoom> {
    return this.rooms;
  }

  getAllAsArray(): GameRoom[] {
    return Array.from(this.rooms.values());
  }

  create(id: string, name: string, host: Player): GameRoom {
    const trimmedName = (name ?? 'Unnamed Room').trim();
    const created = new Date().toISOString();
    const inviteCode = nanoid(5);
    
    const room: GameRoom = {
      id,
      name: trimmedName,
      host: host.id,
      inviteCode,
      players: new Map([[host.id, host]]),
      gameState: 'waiting',
      difficulty: 'medium',
      maxPlayers: 4,
      currentRound: 0,
      totalRounds: 10,
      created,
      private: true,
      previouslyConnectedMembers: [{ userId: host.id, username: host.name }],
      settings: {
        timeLimit: 30,
        pointsPerCorrect: 100,
        pointsPerSpeed: 50
      }
    };
    
    this.set(id, room);
    return room;
  }

  addPlayer(roomId: string, player: Player): boolean {
    const room = this.get(roomId);
    if (!room) return false;
    
    if (room.players.size >= room.maxPlayers) return false;
    
    player.roomId = roomId;
    room.players.set(player.id, player);
    
    // Add to previously connected members if not already there
    const existingMember = room.previouslyConnectedMembers.find(m => m.userId === player.id);
    if (!existingMember) {
      room.previouslyConnectedMembers.push({ userId: player.id, username: player.name });
    }
    
    this.set(roomId, room);
    return true;
  }

  removePlayer(roomId: string, playerId: string): boolean {
    const room = this.get(roomId);
    if (!room) return false;
    
    const removed = room.players.delete(playerId);
    if (removed) {
      // If the host left, assign new host
      if (room.host === playerId && room.players.size > 0) {
        const newHost = Array.from(room.players.values())[0];
        room.host = newHost.id;
        newHost.isHost = true;
      }
      
      this.set(roomId, room);
    }
    
    return removed;
  }

  startGame(roomId: string): boolean {
    const room = this.get(roomId);
    if (!room || room.gameState !== 'waiting') return false;
    
    room.gameState = 'starting';
    room.currentRound = 1;
    
    // Generate first question
    const questionData = generateQuestion(room.difficulty);
    room.currentQuestion = {
      currentCountry: questionData.currentCountry,
      options: questionData.options,
      questionNumber: 1,
      timeLimit: room.settings.timeLimit,
      startTime: Date.now()
    };
    
    // Reset all player scores and answer states
    room.players.forEach(player => {
      player.score = 0;
      player.hasAnswered = false;
      player.lastAnswerTime = undefined;
    });
    
    room.gameState = 'playing';
    this.set(roomId, room);
    return true;
  }

  submitAnswer(roomId: string, playerId: string, isCorrect: boolean): boolean {
    const room = this.get(roomId);
    if (!room || room.gameState !== 'playing') return false;
    
    const player = room.players.get(playerId);
    if (!player || player.hasAnswered) return false;
    
    player.hasAnswered = true;
    player.lastAnswerTime = Date.now();
    
    if (isCorrect) {
      let points = room.settings.pointsPerCorrect;
      
      // Add speed bonus if answered quickly
      if (room.currentQuestion?.startTime) {
        const answerTime = Date.now() - room.currentQuestion.startTime;
        const timeLimit = (room.settings.timeLimit || 30) * 1000;
        const speedBonus = Math.max(0, Math.floor((timeLimit - answerTime) / 1000) * 5);
        points += Math.min(speedBonus, room.settings.pointsPerSpeed);
      }
      
      player.score += points;
    }
    
    this.set(roomId, room);
    return true;
  }

  nextQuestion(roomId: string): boolean {
    const room = this.get(roomId);
    if (!room || room.gameState !== 'playing') return false;
    
    room.currentRound++;
    
    if (room.currentRound > room.totalRounds) {
      room.gameState = 'finished';
      room.currentQuestion = undefined;
    } else {
      // Generate next question
      const questionData = generateQuestion(room.difficulty);
      room.currentQuestion = {
        currentCountry: questionData.currentCountry,
        options: questionData.options,
        questionNumber: room.currentRound,
        timeLimit: room.settings.timeLimit,
        startTime: Date.now()
      };
      
      // Reset answer states
      room.players.forEach(player => {
        player.hasAnswered = false;
        player.lastAnswerTime = undefined;
      });
    }
    
    this.set(roomId, room);
    return true;
  }

  getRoomByInviteCode(inviteCode: string): GameRoom | undefined {
    return Array.from(this.rooms.values()).find(room => room.inviteCode === inviteCode);
  }

  getPreviouslyConnectedUser(userId: UserId, roomId: RoomId): { userId: UserId; username: string } | null {
    if (!userId || !roomId) return null;
    const room = this.get(roomId);
    const users = room?.previouslyConnectedMembers;
    if (!users) return null;
    return users.find(user => user.userId === userId) ?? null;
  }

  update(id: string, newRoom: Partial<GameRoom>): GameRoom {
    const room = this.rooms.get(id) as GameRoom;
    if (!newRoom) return room;

    const updatedRoom = {
      ...room,
      ...newRoom,
    };
    this.set(id, updatedRoom);
    return updatedRoom;
  }
}

export const roomsSource = new RoomsSource();
export { Player, GameRoom };
