This file is a merged representation of the entire codebase, combined into a single document by Repomix.
The content has been processed where security check has been disabled.

================================================================
File Summary
================================================================

Purpose:
--------
This file contains a packed representation of the entire repository's contents.
It is designed to be easily consumable by AI systems for analysis, code review,
or other automated processes.

File Format:
------------
The content is organized as follows:
1. This summary section
2. Repository information
3. Directory structure
4. Repository files (if enabled)
5. Multiple file entries, each consisting of:
  a. A separator line (================)
  b. The file path (File: path/to/file)
  c. Another separator line
  d. The full contents of the file
  e. A blank line

Usage Guidelines:
-----------------
- This file should be treated as read-only. Any changes should be made to the
  original repository files, not this packed version.
- When processing this file, use the file path to distinguish
  between different files in the repository.
- Be aware that this file may contain sensitive information. Handle it with
  the same level of security as you would the original repository.

Notes:
------
- Some files may have been excluded based on .gitignore rules and Repomix's configuration
- Binary files are not included in this packed representation. Please refer to the Repository Structure section for a complete list of file paths, including binary files
- Files matching patterns in .gitignore are excluded
- Files matching default ignore patterns are excluded
- Security check has been disabled - content may contain sensitive information
- Files are sorted by Git change count (files with more changes are at the bottom)


================================================================
Directory Structure
================================================================
.husky/
  pre-commit
public/
  next-assets/
    images/
      synkro_placeholder.svg
      synkro-3.svg
  robots.txt
server/
  src/
    handlers/
      middleware.ts
      public-rooms.ts
      socket-events.ts
    utils/
      chat.ts
      cleanup.ts
      logger.ts
      room-management.ts
      user-management.ts
      utils.ts
    app.ts
  .eslintrc.js
  .gitignore
  .prettierrc
  package.json
  README.md
  tsconfig.json
src/
  components/
    Header/
      NavigationHeader.tsx
      QRCodeModal.tsx
      VideoRoomHeader.tsx
    Modals/
      UserModal.tsx
    ui/
      aspect-ratio.tsx
      button.tsx
      dialog.tsx
      input.tsx
      label.tsx
      switch.tsx
      toast.tsx
      toaster.tsx
      tooltip.tsx
      use-toast.ts
    VideoRoom/
      Chat.tsx
      PlayPauseButton.tsx
      Queue.tsx
      RoomToolbar.tsx
      Settings.tsx
    CraftedWithLove.tsx
    CreateRoomBox.tsx
    DiceButton.tsx
    ErrorBoundary.tsx
    EyeButton.tsx
    JoinRoomBox.tsx
    Page.tsx
    PageHead.tsx
    Separator.tsx
    Sidebar.tsx
    Spinner.tsx
    StarField.tsx
  constants/
    constants.ts
    socketActions.ts
  context/
    SocketContext.tsx
    SocketProvider.tsx
  hooks/
    useAudio.ts
    useCopyToClipboard.ts
    useDarkMode.ts
    useEventCallback.ts
    useEventListener.ts
    useIsFirstRender.ts
    useIsomorphicLayoutEffect.ts
    useLocalStorage.ts
    useMediaQuery.ts
    useQueue.ts
    useSSE.ts
    useUpdateEffect.ts
  libs/
    utils/
      chat.ts
      frontend-utils.ts
      names.ts
      socket.ts
      video-fetch-lib.ts
  mock/
    mockMessages.json
  pages/
    api/
      audio-proxy.ts
      authenticate.ts
      og.tsx
      video-proxy.ts
    invite/
      [code]/
        index.tsx
    room/
      [id]/
        index.tsx
    rooms/
      index.tsx
    _app.tsx
    _document.tsx
    _error.tsx
    404.tsx
    index.tsx
  styles/
    global.css
  types/
    interfaces.ts
    socketCustomTypes.ts
  instrumentation.ts
  middleware.ts
.eslintrc.json
.gitignore
.vercelignore
components.json
Dockerfile
eslint.config.mjs
next.config.ts
package.json
postcss.config.js
README.md
tailwind.config.js
tsconfig.json

================================================================
Files
================================================================

================
File: .husky/pre-commit
================
pnpm exec lint-staged

================
File: public/next-assets/images/synkro_placeholder.svg
================
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="1080" fill="#67657B"/>
<path d="M1034.01 394.653H1181.03V709.151H1034.01V394.653Z" fill="#F583FF"/>
<path d="M886.989 394.653H1034.01V709.151H886.989V394.653Z" fill="#B1BEFF"/>
<path d="M739.967 394.653H886.989V709.151H739.967V394.653Z" fill="#EEFEC1"/>
<path d="M1193.27 380.773C1193.27 379.523 1192.27 378.527 1191.01 378.527H729.985C729.689 378.524 729.396 378.581 729.121 378.692C728.847 378.804 728.598 378.969 728.388 379.178C728.178 379.387 728.011 379.636 727.898 379.91C727.785 380.183 727.727 380.477 727.727 380.773V723.613C727.727 724.863 728.728 725.859 729.985 725.859H1191.01C1191.31 725.862 1191.6 725.805 1191.88 725.693C1192.15 725.582 1192.4 725.417 1192.61 725.208C1192.82 724.999 1192.99 724.75 1193.1 724.476C1193.22 724.202 1193.27 723.909 1193.27 723.613V380.773Z" stroke="#3B4866" stroke-width="41.336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1076.89 262.75L960.5 378.527L844.114 262.75" stroke="#3B4866" stroke-width="41.336" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

================
File: public/next-assets/images/synkro-3.svg
================
<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="1920" height="1080" fill="url(#paint0_linear_104_17)"/>
<path d="M1034.01 394.653H1181.03V709.151H1034.01V394.653Z" fill="#F583FF"/>
<path d="M886.989 394.653H1034.01V709.151H886.989V394.653Z" fill="#B1BEFF"/>
<path d="M739.967 394.653H886.989V709.151H739.967V394.653Z" fill="#EEFEC1"/>
<path d="M1193.27 380.773C1193.27 379.523 1192.27 378.527 1191.01 378.527H729.985C729.689 378.524 729.396 378.581 729.121 378.692C728.847 378.804 728.598 378.969 728.388 379.178C728.178 379.387 728.011 379.636 727.898 379.91C727.785 380.183 727.727 380.477 727.727 380.773V723.613C727.727 724.863 728.728 725.859 729.985 725.859H1191.01C1191.31 725.862 1191.6 725.805 1191.88 725.693C1192.15 725.582 1192.4 725.417 1192.61 725.208C1192.82 724.999 1192.99 724.75 1193.1 724.476C1193.22 724.202 1193.27 723.909 1193.27 723.613V380.773Z" stroke="#233C76" stroke-width="41.336" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M1076.89 262.75L960.5 378.527L844.114 262.75" stroke="#233C76" stroke-width="41.336" stroke-linecap="round" stroke-linejoin="round"/>
<defs>
<linearGradient id="paint0_linear_104_17" x1="960" y1="0" x2="960" y2="1080" gradientUnits="userSpaceOnUse">
<stop stop-color="#18FCFF" stop-opacity="0.75"/>
<stop offset="1" stop-color="#DD15DF"/>
</linearGradient>
</defs>
</svg>

================
File: public/robots.txt
================
# robots.txt

# Allow all crawlers
User-agent: *
Allow: /

================
File: server/src/handlers/middleware.ts
================
import { Request, Response, NextFunction } from 'express';

export const handleVerifyApiKey = (req: Request, res: Response, next: NextFunction) => {
  if (process.env.NODE_ENV === 'development') return next();

  const apiKey = req.headers['x-api-key'];

  if (!apiKey) {
    return res.status(401).json({ error: 'API key is missing' });
  }

  if (apiKey !== process.env.SERVER_API_KEY) {
    return res.status(401).json({ error: 'Invalid API key' });
  }

  next();
};

================
File: server/src/handlers/public-rooms.ts
================
import { Request, Response } from 'express';
import { roomsSource } from '../utils/room-management';

export const publicRoomsHandler = (
  req: Request,
  res: Response,
) => {
  res.writeHead(200, {
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    Connection: 'keep-alive',
  });

  const sendPublicRooms = () => {
    const rooms = roomsSource.getAllAsArray();
    const publicRooms = rooms.filter((room) => !room.private);
    res.write(`data: ${JSON.stringify({ type: 'rooms', rooms: publicRooms })}\n\n`);
  };

  sendPublicRooms();

  roomsSource.on('room:added', sendPublicRooms);
  roomsSource.on('room:updated', sendPublicRooms);
  roomsSource.on('rooms:cleared', sendPublicRooms);
  roomsSource.on('room:deleted', sendPublicRooms);

  req.on('close', () => {
    roomsSource.removeListener('room:added', sendPublicRooms);
    roomsSource.removeListener('room:updated', sendPublicRooms);
    roomsSource.removeListener('rooms:cleared', sendPublicRooms);
    roomsSource.removeListener('room:deleted', sendPublicRooms);
    res.end();
  });
};

================
File: server/src/handlers/socket-events.ts
================
import { nanoid } from 'nanoid';
import { v4 as uuidv4 } from 'uuid';
import ReactPlayer from 'react-player';
import {
  SET_ADMIN,
  CHECK_IF_ROOM_EXISTS,
  CREATE_ROOM,
  GET_ROOM_INFO,
  JOIN_ROOM,
  JOIN_ROOM_BY_INVITE,
  RECONNECT_USER,
  SERVER_MESSAGE,
  USER_MESSAGE,
  GET_VIDEO_INFORMATION,
  GET_HOST_VIDEO_INFORMATION,
  SYNC_VIDEO_INFORMATION,
  PLAY_VIDEO,
  PAUSE_VIDEO,
  BUFFERING_VIDEO,
  USER_VIDEO_STATUS,
  SYNC_TIME,
  REWIND_VIDEO,
  FASTFORWARD_VIDEO,
  END_OF_VIDEO,
  CHANGE_VIDEO,
  ADD_VIDEO_TO_QUEUE,
  REMOVE_VIDEO_FROM_QUEUE,
  VIDEO_QUEUE_REORDERED,
  VIDEO_QUEUE_CLEARED,
  CHANGE_SETTINGS,
  SET_HOST,
  KICK_USER,
  LEAVE_ROOM,
} from '../../../src/constants/socketActions';
import { roomsSource } from '../utils/room-management';
import { requestIsNotFromHost, usersSource } from '../utils/user-management';
import { Room, ServerMessageType, User, VideoStatus } from '../../../src/types/interfaces';
import { ClientToServerEvents, InterServerEvents, ServerToClientEvents, SocketData } from '../../../src/types/socketCustomTypes';
import { Server, Socket } from 'socket.io';

const roomTimeouts: { [roomId: string]: NodeJS.Timeout | undefined } = {};

export const handleSocketEvents = (
  io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  socket: Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
) => {
  const userId = socket.handshake.auth.token;
  const adminTokenHandshake = socket.handshake.auth.adminToken;
  const adminToken = process.env.ADMIN_TOKEN;

  if (!userId) {
    socket.disconnect();
    return;
  }

  if (usersSource.users.has(userId)) {
    socket.disconnect();
    return;
  }

  socket.data = {
    userId: userId || '',
    roomId: '',
    isAdmin: false,
  };

  if (typeof socket.data.userId !== 'string') return;

  if (adminToken === adminTokenHandshake) {
    socket.data.isAdmin = true;
    socket.emit(SET_ADMIN);
  }

  console.log(`⚡️ New user connected - User Id: ${userId}`);

  socket.on(CHECK_IF_ROOM_EXISTS, (roomId, callback) => {
    const room = roomsSource.get(roomId);
    typeof callback === 'function' && callback(room ?? null);
  });

  socket.on(CREATE_ROOM, async (username, roomName, callback) => {
    if (typeof username !== 'string') {
      typeof callback === 'function' && callback({ error: 'Username must be of type string' });
      return;
    } else if (username.length > 80) {
      typeof callback === 'function' && callback({ error: 'Username cannot exceed the maximum character length of 80' });
      return;
    }
    const newRoomId = nanoid(6);
    if (userId) {
      const room = roomsSource.get(newRoomId);
      if (room) {
        typeof callback === 'function' && callback({ error: 'Room already exists' });
      } else {
        if (!socket.data.userId) return;
        const user = usersSource.createUser({
          id: socket.data.userId,
          username,
          roomId: newRoomId,
          socketId: socket.id,
          isAdmin: socket.data.isAdmin,
        });
        console.log(`👀 New user joined in room: ${user.roomId} - User Id: ${userId}`);
        socket.join(newRoomId);
        socket.data.roomId = newRoomId;

        const newRoom = roomsSource.create(newRoomId, roomName, user);

        if (newRoom) {
          roomsSource.set(newRoomId, newRoom);
          typeof callback === 'function' && callback({ result: newRoom });
          socket.emit(GET_ROOM_INFO, newRoom);
        }
      }
    } else {
      typeof callback === 'function' && callback({ error: 'Failed to create room' });
    }
  });

  socket.on(JOIN_ROOM, (roomId, username, callback) => {
    if (!roomId || !username || !socket.data.userId) {
      typeof callback === 'function' && callback({ success: false, error: 'An invalid input was provided' });
      return;
    } else if (typeof username !== 'string') {
      typeof callback === 'function' && callback({ success: false, error: 'Username must be of type string' });
      return;
    } else if (typeof roomId !== 'string') {
      typeof callback === 'function' && callback({ success: false, error: 'Room Id must be of type string' });
      return;
    } else if (username.length > 80) {
      typeof callback === 'function' && callback({ success: false, error: 'Username cannot exceed the maximum character length of 80' });
      return;
    } else if (roomId.length !== 6) {
      typeof callback === 'function' && callback({ success: false, error: 'Room Id must have a character length of 6' });
      return;
    }

    const existingRoom = roomsSource.has(roomId);
    if (!existingRoom) {
      typeof callback === 'function' && callback({ success: false, error: `Failed to find room: ${roomId}` });
      return;
    }

    const updatedRoom = addUserToRoom(io, socket, socket.data.userId, roomId, username);
    if (updatedRoom && typeof callback === 'function') {
      callback({ success: true });
    }
  });

  socket.on(JOIN_ROOM_BY_INVITE, (inviteCode, username, roomPasscode, callback) => {
    if (!inviteCode || !username || !socket.data.userId) {
      typeof callback === 'function' && callback({ success: false, error: 'An invalid input was provided' });
      return;
    } else if (inviteCode.length !== 5) {
      typeof callback === 'function' && callback({ success: false, error: 'Invite code must have a character length of 5' });
      return;
    }

    const room = roomsSource.getRoomByInviteCode(inviteCode);
    if (!room) {
      typeof callback === 'function' &&
        callback({
          success: false,
          error: 'Invite code is invalid or this room no longer exists',
        });
      return;
    }

    if (room.passcode) {
      if (roomPasscode.trim() !== room.passcode) {
        callback({ success: false, error: 'Incorrect passcode' });
        return;
      }
    }

    const updatedRoom = addUserToRoom(io, socket, socket.data.userId, room.id, username);

    if (updatedRoom && typeof callback === 'function') {
      callback({ success: true, roomId: room.id });
    }
  });

  socket.on(RECONNECT_USER, (roomId, userId, callback) => {
    if (roomId && userId) {
      const existingRoom = roomsSource.get(roomId);

      if (!existingRoom) {
        typeof callback === 'function' && callback({ success: false, error: `Failed to find room: ${roomId}` });
        return;
      }

      const previouslyConnectedUser = roomsSource.getPreviouslyConnectedUser(userId, roomId);

      if (previouslyConnectedUser) {
        socket.data.userId = userId;
        socket.data.roomId = roomId;
        socket.join(roomId);

        const timestamp = new Date().toISOString();
        io.to(roomId).emit(SERVER_MESSAGE, {
          type: ServerMessageType.USER_RECONNECTED,
          message: `${previouslyConnectedUser.username} reconnected`,
          timestamp,
        });

        const user: User = usersSource.createUser({
          id: userId,
          username: previouslyConnectedUser.username,
          roomId: roomId,
          socketId: socket.id,
          isAdmin: socket.data.isAdmin,
        });

        const userExistsInRoomMembers = existingRoom.members.find((member) => member.id === user.id);
        const newMembers = userExistsInRoomMembers ? existingRoom.members : [...existingRoom.members, user];
        const updatedRoom = roomsSource.update(roomId, {
          ...existingRoom,
          members: newMembers,
        });

        if (newMembers.length === 1) {
          updatedRoom.host = userId;
          const timestamp = new Date().toISOString();
          io.in(roomId).emit(SERVER_MESSAGE, {
            type: ServerMessageType.NEW_HOST,
            message: `${previouslyConnectedUser.username} is now the host. 👑`,
            timestamp,
          });
        }

        roomsSource.set(roomId, updatedRoom);
        if (roomTimeouts[roomId]) {
          clearTimeout(roomTimeouts[roomId]);
          delete roomTimeouts[roomId];
        }

        io.to(roomId).emit(GET_ROOM_INFO, updatedRoom);

        return;
      }

      typeof callback === 'function' && callback({ success: false, error: `You are not authorized to connect to this room: ${roomId}` });
    } else {
      const errorMessage = !roomId && !userId ? 'No room or user id was provided' : !roomId ? 'No room id was provided' : 'No user id was provided';
      typeof callback === 'function' && callback({ success: false, error: errorMessage });
    }
  });

  socket.on(USER_MESSAGE, (message, roomId) => {
    if (message.length > 500) {
      console.error(`Message length cannot be greater than 500 - UserId: ${socket.data.userId}`);
      return;
    }
    console.log(`📩 Received message: ${message} in ${roomId} by ${socket.data.userId}`);
    const user = socket.data.userId && usersSource.get(socket.data.userId);
    if (user && socket.data.userId) {
      const timestamp = new Date().toISOString();
      const messageID = uuidv4();
      io.in(roomId).emit(USER_MESSAGE, {
        username: user.username,
        message,
        userId: socket.data.userId,
        id: messageID,
        timestamp,
        color: user.color,
        type: 'USER',
        isAdmin: socket.data.isAdmin || false,
      });
    }
  });

  socket.on(GET_VIDEO_INFORMATION, () => {
    if (!requestIsNotFromHost(socket, roomsSource.rooms)) return;
    if (!socket?.data?.roomId) return;

    const room = roomsSource.get(socket.data.roomId);
    if (!room) return;

    const host = usersSource.get(room.host);
    if (!host) return;

    io.sockets.sockets.get(host.socketId)?.emit(GET_HOST_VIDEO_INFORMATION, (playing, videoUrl, elapsedVideoTime, eventCalledTime) => {
      //socket.emit(SYNC_VIDEO_INFORMATION, playing, videoUrl, time);
      socket.emit(SYNC_VIDEO_INFORMATION, playing, room.videoInfo.currentVideoUrl || videoUrl, elapsedVideoTime, eventCalledTime);
    });
  });

  socket.on(PLAY_VIDEO, () => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;

    const user = socket.data.userId && usersSource.get(socket.data.userId);
    if (user && user.roomId) {
      socket.to(user.roomId).emit(PLAY_VIDEO);

      socket.emit(GET_HOST_VIDEO_INFORMATION, (playing, videoUrl, elapsedVideoTime, eventCalledTime) => {
        socket.to(user.roomId).emit(SYNC_VIDEO_INFORMATION, playing, videoUrl, elapsedVideoTime, eventCalledTime);
      });
    }
  });

  socket.on(PAUSE_VIDEO, () => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;

    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user.roomId) {
      socket.to(user.roomId).emit(PAUSE_VIDEO);
    }
  });

  socket.on(BUFFERING_VIDEO, (time) => {
    const user = socket.data.userId && usersSource.get(socket.data.userId);
    if (requestIsNotFromHost(socket, roomsSource.rooms) && user && user.roomId && socket.data.userId) {
      io.to(user.roomId).emit(USER_VIDEO_STATUS, socket.data.userId, VideoStatus.BUFFERING);
    } else if (user && user.roomId) {
      socket.to(user.roomId).emit(SYNC_TIME, time);
      socket.to(user.roomId).emit(PLAY_VIDEO);

      // const room = roomsSource.get(user.roomId);
      // if (room) {
      //   io.sockets.sockets.get(room.host)?.emit(GET_HOST_VIDEO_INFORMATION, (playing) => {
      //     socket.to(user.roomId).emit(SYNC_TIME, time);
      //     socket.to(user.roomId).emit(PLAY_VIDEO);
      //   });
      // }
    }
  });

  socket.on(REWIND_VIDEO, (time) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user.roomId) {
      socket.to(user.roomId).emit(REWIND_VIDEO, time);
    }
  });

  socket.on(FASTFORWARD_VIDEO, (time) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user.roomId) {
      socket.to(user.roomId).emit(FASTFORWARD_VIDEO, time);
    }
  });

  socket.on(END_OF_VIDEO, () => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;

      const nextIndex = room.videoInfo.currentQueueIndex + 1;
      const nextVideo = room.videoInfo.queue[nextIndex] ?? room.videoInfo.queue[0];

      if (nextVideo) {
        room.videoInfo.currentQueueIndex = nextIndex < room.videoInfo.queue.length ? nextIndex : 0;
        room.videoInfo.currentVideoUrl = nextVideo.url;
        io.to(user.roomId).emit(CHANGE_VIDEO, nextVideo.url);
      }
    }
  });

  socket.on(CHANGE_VIDEO, async (url, newIndex) => {
    if (!ReactPlayer.canPlay(url)) return;
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (room) {
        room.videoInfo.currentVideoUrl = url;
        if (typeof newIndex === 'number' && newIndex > -1) {
          room.videoInfo.currentQueueIndex = newIndex;
        }
        await socket.in(user.roomId).emit(CHANGE_VIDEO, url);
        socket.emit(GET_HOST_VIDEO_INFORMATION, (playing: boolean, videoUrl: string) => {
          io.to(room.id).emit(GET_ROOM_INFO, room);
          // TODO: TEST THIS
          io.to(room.id).emit(SYNC_VIDEO_INFORMATION, playing, videoUrl, 0, 0);
        });
      }
    }
  });

  socket.on(ADD_VIDEO_TO_QUEUE, (newVideo) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;
      room.videoInfo.queue = [...room.videoInfo.queue, newVideo];
      socket.to(user.roomId).emit(ADD_VIDEO_TO_QUEUE, newVideo);
    }
  });

  socket.on(REMOVE_VIDEO_FROM_QUEUE, (url) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;
      const newVideoQueue = room.videoInfo.queue.filter((videoItem) => videoItem.url !== url);
      room.videoInfo.queue = newVideoQueue;
      socket.to(user.roomId).emit(REMOVE_VIDEO_FROM_QUEUE, url);
    }
  });

  socket.on(VIDEO_QUEUE_REORDERED, (newVideoQueue) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;
      room.videoInfo.queue = newVideoQueue;
      socket.to(user.roomId).emit(VIDEO_QUEUE_REORDERED, newVideoQueue);
    }
  });

  socket.on(VIDEO_QUEUE_CLEARED, () => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;
      if (room.videoInfo.queue.length > 0) {
        room.videoInfo.queue = [];
        socket.to(user.roomId).emit(VIDEO_QUEUE_REORDERED, []);
      }
    }
  });

  socket.on(CHANGE_SETTINGS, (newSettings) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    const user = socket?.data?.userId && usersSource.get(socket.data.userId);
    if (user && user?.roomId) {
      const room = roomsSource.get(user.roomId);
      if (!room) return;
      if (newSettings.maxRoomSize && newSettings.maxRoomSize <= 20) {
        room.maxRoomSize = newSettings.maxRoomSize;
      }
      room.passcode = newSettings.roomPasscode;
      room.private = newSettings.private;

      io.to(user.roomId).emit(GET_ROOM_INFO, room);
    }
  });

  socket.on(SET_HOST, (userId) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    if (!socket.data.roomId) return;

    const user = usersSource.get(userId);
    if (!user) return;

    const room = roomsSource.get(user.roomId);
    if (!room) return;
    room.host = userId;
    roomsSource.set(socket.data.roomId, room);

    io.in(room.id).emit(GET_ROOM_INFO, room);
    const timestamp = new Date().toISOString();
    io.in(room.id).emit(SERVER_MESSAGE, {
      type: ServerMessageType.NEW_HOST,
      message: `${user.username} is now the host. 👑`,
      timestamp,
    });
  });

  socket.on(KICK_USER, (userId) => {
    if (requestIsNotFromHost(socket, roomsSource.rooms)) return;
    if (!socket.data.roomId) return;

    const user = usersSource.get(userId);
    if (!user || user.isAdmin) return;

    handleUserDisconnect(userId, io);
    io.sockets.sockets.get(user.socketId)?.emit(KICK_USER);
    io.sockets.sockets.get(user.socketId)?.leave(user.roomId);
  });

  socket.on(LEAVE_ROOM, () => {
    if (socket.data.userId) {
      handleUserDisconnect(socket.data.userId, io);
      socket.data.roomId && socket?.emit(LEAVE_ROOM);
      socket.data.roomId && socket.leave(socket.data.roomId);
      socket.data.userId = undefined;
      socket.data.roomId = undefined;
    }
  });

  socket.on('disconnect', () => {
    if (socket.data.userId) {
      handleUserDisconnect(socket.data.userId, io);
      socket.data.roomId && socket.leave(socket.data.roomId);
      socket.data.userId = undefined;
      socket.data.roomId = undefined;
    }
  });
};

// export type CustomIO = Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
// export type CustomSocket = Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;

const handleUserDisconnect = (userId: string, io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>) => {
  if (!userId) return;

  const user = usersSource.get(userId);
  if (user) {
    const timestamp = new Date().toISOString();
    io.to(user.roomId).emit(SERVER_MESSAGE, {
      type: ServerMessageType.USER_DISCONNECTED,
      message: `${user.username} has disconnected`,
      timestamp,
    });

    const room = roomsSource.get(user.roomId);

    if (room) {
      const userWasHost = userId === room.host;

      const newMembers = room.members.filter((member) => member.id !== userId);
      const updatedRoom = roomsSource.update(user.roomId, { members: newMembers });
      roomsSource.set(user.roomId, updatedRoom);

      const THREE_MINUTES = 3 * 60 * 1000;

      if (newMembers.length === 0) {
        roomsSource.set(user.roomId, { ...updatedRoom, members: [] });
        roomTimeouts[user.roomId] = setTimeout(async () => {
          if (updatedRoom.members.length === 0) {
            roomsSource.delete(user.roomId);
            console.log(`🧼 Cleanup: Room ${user.roomId} has been deleted.`);
          }
        }, THREE_MINUTES);
      } else {
        roomTimeouts[user.roomId] && clearTimeout(roomTimeouts[user.roomId]);
      }

      const updatedUsers = new Map(
        Array.from(usersSource.users.values())
          .filter((user) => user.id !== userId)
          .map((user) => [user.id, user]),
      );
      usersSource.setUsers(updatedUsers);

      if (userWasHost && newMembers.length > 0) {
        updatedRoom.host = newMembers[0].id;
        roomsSource.set(user.roomId, updatedRoom);

        const timestamp = new Date().toISOString();
        io.in(room.id).emit(SERVER_MESSAGE, {
          type: ServerMessageType.NEW_HOST,
          message: `${newMembers[0].username} is now the host. 👑`,
          timestamp,
        });
      }

      io.to(room.id).emit(GET_ROOM_INFO, updatedRoom);
    }

    const roomInfo = roomsSource.get(user.roomId);
    const activeConnections = io.sockets.sockets.size;
    console.log(
      `👻 User disconnected - User Id: ${userId} - Room Id: ${user.roomId}`,
      roomInfo?.members?.length,
      usersSource.getLength(),
      activeConnections,
    );
  }
};

const addUserToRoom = (
  io: Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  socket: Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  userId: string,
  roomId: string,
  username: string,
): Room | null => {
  const room = roomsSource.get(roomId);
  if (!room) return null;

  console.log(`👀 New user joined in room: ${roomId} - User Id: ${userId}`);

  socket.join(roomId);
  socket.data.roomId = roomId;

  const existingUser = usersSource.get(userId);
  if (existingUser) {
    const timestamp = new Date().toISOString();
    io.to(roomId).emit(SERVER_MESSAGE, {
      type: ServerMessageType.USER_RECONNECTED,
      message: `${existingUser.username} has reconnected`,
      timestamp,
    });
    //return room;
  }

  const user = usersSource.createUser({ id: userId, username, roomId, socketId: socket.id, isAdmin: socket.data.isAdmin });

  const userExistsInRoomMembers = room.members.find((member) => member.id === user.id);
  const newMembers = userExistsInRoomMembers ? room.members : [...room.members, user];
  const filteredPreviouslyConnectedMembers = room.previouslyConnectedMembers.filter((member) => member.userId !== user.id);
  const updatedRoom = roomsSource.update(roomId, {
    ...room,
    members: newMembers,
    previouslyConnectedMembers: [...filteredPreviouslyConnectedMembers, { userId: user.id, username: user.username }],
  });

  if (newMembers.length === 1) {
    updatedRoom.host = userId;
  }

  roomsSource.set(roomId, updatedRoom);
  if (roomTimeouts[roomId]) {
    clearTimeout(roomTimeouts[roomId]);
    roomTimeouts[roomId] = undefined;
  }

  io.to(roomId).emit(GET_ROOM_INFO, updatedRoom);

  const timestamp = new Date().toISOString();
  io.to(roomId).emit(SERVER_MESSAGE, {
    type: ServerMessageType.USER_JOINED,
    message: `${username} has joined the room`,
    timestamp,
  });

  return updatedRoom;
};

================
File: server/src/utils/chat.ts
================
import { User } from '../../../src/types/interfaces';

export const usernameChatColors = [
  '#57cc99',
  '#d9d2ff',
  '#ff5400',
  '#ffe1a8',
  '#119f11',
  '#65debe',
  '#ee6c21',
  '#f8e33f',
  '#65debe',
  '#b969cc',
  '#3591fc',
  '#f189c9',
  '#c23a21',
  '#9ba2fd',
  '#ff5c8a',
  '#aaf683',
  '#ffee32',
  '#a5ffd6',
  '#84dcc6',
  '#38b000',
  '#f79d65',
  '#d84727',
  '#c0fdfb',
  '#c05299',
  '#d2de32',
  '#fcf300',
  '#5bdb70',
];

const getRandomInt = (min: number, max: number) => Math.floor(Math.random() * (max - min)) + min;

export const assignUsernameChatColor = (members: User[]): string => {
  const currentlyUsedColors = members.map((member) => member.color);
  const unusedColors = usernameChatColors.filter((usernameChatColor) => !currentlyUsedColors.includes(usernameChatColor));
  const randomColor = unusedColors[getRandomInt(0, unusedColors.length)];
  return randomColor;
};

================
File: server/src/utils/cleanup.ts
================
import { roomsSource } from './room-management';

const CLEANUP_INTERVAL = 4 * 60 * 1000;
let cleanupInterval: NodeJS.Timeout | null = null;

export const startCleanupInterval = () => {
  if (!cleanupInterval && roomsSource.getLength() > 0) {
    cleanupInterval = setInterval(() => {
      for (const roomId in roomsSource.rooms) {
        if (roomsSource.get(roomId)?.members.length === 0) {
          roomsSource.delete(roomId);
          console.log(`🧼 Cleanup: Room ${roomId} has been deleted.`);
        }
      }
      if (roomsSource.getLength() === 0 && cleanupInterval) {
        clearInterval(cleanupInterval);
        cleanupInterval = null;
        console.log('🛑 Cleanup interval stopped as there are no rooms left');
      }
    }, CLEANUP_INTERVAL);
  }
};

================
File: server/src/utils/logger.ts
================
import winston from 'winston';

const levels = {
  error: 0,
  warn: 1,
  info: 2,
  http: 3,
  debug: 4,
};

const colors = {
  error: 'red',
  warn: 'yellow',
  info: 'green',
  http: 'magenta',
  debug: 'white',
};

winston.addColors(colors);
const format = winston.format.combine(
  winston.format.timestamp({ format: 'YYYY-MM-DD HH:mm:ss:ms' }),
  winston.format.colorize({ all: true }),
  winston.format.printf((info) => `${info.timestamp} ${info.level}: ${info.message}`),
);

const transports = [new winston.transports.Console()];
const Logger = winston.createLogger({
  levels,
  format,
  transports,
});

export default Logger;

================
File: server/src/utils/room-management.ts
================
import { EventEmitter } from 'events';
import { nanoid } from 'nanoid';
import { Room, RoomId, User, UserId } from '../../../src/types/interfaces';
//import mockRoom from './mockRoom';

export class RoomsSource extends EventEmitter {
  public rooms: Map<string, Room> = new Map();

  constructor() {
    super();

    //this.rooms.set('mBbvT-', mockRoom);
  }

  clear(): void {
    this.rooms.clear();
    this.emit('rooms:cleared');
  }

  delete(id: string): void {
    const room = this.rooms.get(id);
    if (room) {
      this.rooms.delete(id);
      this.emit('room:deleted', room);
    }
  }

  get(id: string): Room | undefined {
    return this.rooms.get(id);
  }

  has(id: string): boolean {
    return this.rooms.has(id);
  }

  set(id: string, room: Room): void {
    const existingRoom = this.rooms.get(id);
    this.rooms.set(id, room);
    this.emit(existingRoom ? 'room:updated' : 'room:added', room);
  }

  getLength(): number {
    return Array.from(this.rooms.values()).length;
  }

  getAll(): Map<string, Room> {
    return this.rooms;
  }

  getAllAsArray(): Room[] {
    return Array.from(this.rooms.values());
  }

  getPreviouslyConnectedUser = (userId: UserId, roomId: RoomId): { userId: UserId; username: string } | null => {
    if (!userId || !roomId) return null;
    const room = this.get(roomId);
    const users = room?.previouslyConnectedMembers;
    if (!users) return null;
    const result = users.find((user) => user.userId === userId) ?? null;
    return result;
  };

  getPreviouslyConnectedUsers = (roomId: RoomId): { userId: string; username: string }[] | null => {
    if (!roomId) return null;
    const room = this.get(roomId);
    const previouslyConnectedMemberIds = room?.previouslyConnectedMembers.map((member) => member.userId);
    const result =
      room?.members
        .filter((user) => previouslyConnectedMemberIds?.includes(user.id))
        .map((user) => {
          return { userId: user.id, username: user.username };
        }) ?? null;
    return result;
  };

  create(id: string, name: string, user: User): Room {
    const trimmedName = (name ?? 'Unnamed')?.trim();
    const created = new Date().toISOString();
    const inviteCode = nanoid(5);
    const room: Room = {
      host: user.id,
      name: trimmedName,
      id,
      inviteCode,
      videoInfo: {
        currentVideoUrl: 'https://youtu.be/QdKhuEnkwiY',
        currentQueueIndex: -1,
        queue: [],
      },
      maxRoomSize: 10,
      members: [user],
      passcode: null,
      created,
      private: true,
      previouslyConnectedMembers: [{ userId: user.id, username: user.username }],
    };
    this.set(id, room);
    this.emit('room:added', room);
    return room;
  }

  update(id: string, newRoom: Partial<Room>): Room {
    const room = this.rooms.get(id) as Room;
    if (!newRoom) return room;

    const updatedRoom = {
      ...room,
      ...newRoom,
    };
    this.set(id, updatedRoom);
    this.emit('room:updated', updatedRoom);
    return updatedRoom;
  }

  getRoomByInviteCode(inviteCode: string): Room | undefined {
    const room = Array.from(this.rooms.values()).find((room) => room.inviteCode === inviteCode);
    return room;
  }
}

export const roomsSource = new RoomsSource();

================
File: server/src/utils/user-management.ts
================
import { EventEmitter } from 'events';
import { Room, User } from '../../../src/types/interfaces';
import { ClientToServerEvents, InterServerEvents, ServerToClientEvents, SocketData } from '../../../src/types/socketCustomTypes';
import { assignUsernameChatColor } from './chat';
import { Socket } from 'socket.io';

export class UsersSource extends EventEmitter {
  public users: Map<string, User> = new Map();
  public length: () => number = () => this.getLength();

  clear(): void {
    this.users.clear();
    this.emit('users:cleared');
  }

  delete(id: string): void {
    const user = this.users.get(id);
    if (user) {
      this.users.delete(id);
      this.emit('user:deleted', user);
    }
  }

  get(id: string): User | undefined {
    return this.users.get(id);
  }

  has(id: string): boolean {
    return this.users.has(id);
  }

  set(id: string, user: User): void {
    const existingUser = this.users.get(id);
    this.users.set(id, user);
    this.emit(existingUser ? 'users:updated' : 'users:added', user);
  }

  setUsers(users: Map<string, User>): void {
    this.users = users;
    this.emit('users:updated');
  }

  getLength(): number {
    return Array.from(this.users.values()).length;
  }

  getAll(): Map<string, User> {
    return this.users;
  }

  getAllAsArray(): User[] {
    return Array.from(this.users.values());
  }

  createUser = ({
    id,
    username,
    roomId,
    socketId,
    isAdmin,
  }: {
    id: string;
    username: string;
    roomId: string;
    socketId: string;
    isAdmin?: boolean;
  }): User => {
    const created = new Date().toISOString();
    const usersInSameRoom = Array.from(this.users.values()).filter((user) => user.roomId === roomId);
    const user: User = { id, username, roomId, created, socketId, color: assignUsernameChatColor(usersInSameRoom), isAdmin };
    const existingUser = this.users.get(id);
    if (!existingUser) this.users.set(id, user);
    return user;
  };
}

export const usersSource = new UsersSource();

export const requestIsNotFromHost = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>,
  rooms: Map<string, Room>,
  adminCheck: boolean = false,
): boolean => {
  if (adminCheck && socket.data?.isAdmin) return false;
  const room = !!socket?.data?.roomId && rooms.get(socket.data?.roomId);
  return Boolean(room && socket.data?.userId !== room.host);
};

================
File: server/src/utils/utils.ts
================
export const mapToObject = <K extends string | number, V>(map: Map<K, V>): Record<K, V> => {
  const object: Record<K, V> = {} as Record<K, V>;
  map.forEach((value, key) => {
    object[key] = value;
  });
  return object;
};

================
File: server/src/app.ts
================
import express, { Application } from 'express';
import { createServer, Server as HttpServer } from 'http';
import cors from 'cors';
import { Server } from 'socket.io';
import { InterServerEvents, ServerToClientEvents, ClientToServerEvents, SocketData } from '../../src/types/socketCustomTypes';
import { roomsSource } from './utils/room-management';
import { usersSource } from './utils/user-management';
import { handleSocketEvents } from './handlers/socket-events';
import { startCleanupInterval } from './utils/cleanup';
import { publicRoomsHandler } from './handlers/public-rooms';

import { config } from 'dotenv';
import { handleVerifyApiKey } from './handlers/middleware';
config();

const PORT = (process.env.PORT && parseInt(process.env.PORT)) || 8000;
const app: Application = express();
const server: HttpServer = createServer(app);

const allowedOrigins = [
  'http://localhost:3000',
  'https://synkro.live/',
  'https://www.synkro.live/',
  'https://synkro.vercel.app',
  'https://synkro-synkro.vercel.app',
  'https://synkro-git-master-synkro.vercel.app',
];

const corsOptions = {
  origin: allowedOrigins,
  methods: ['GET', 'POST'],
};

app.use(cors(corsOptions));

const io = new Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>(server, {
  allowRequest: (req, callback) => {
    const isAllowedOrigin = req?.headers?.origin ? allowedOrigins.includes(req.headers.origin) : false;
    callback(null, isAllowedOrigin);
  },
});

io.use((socket, next) => {
  const userId = socket.handshake.auth.token;
  if (userId) {
    next();
  } else {
    next(new Error('Missing session token'));
  }
});

io.on('connection', (socket) => {
  handleSocketEvents(io, socket);
});

startCleanupInterval();

server.listen(PORT, () => {
  console.log(`🚀 WebSocket server is running on http://localhost:${PORT}`);
});

app.get('/api/healthz', (_req, res) => {
  res.send({ status: 'ok' });
});

app.get('/api/rooms', handleVerifyApiKey, (_req, res) => {
  res.json({ rooms: Object.fromEntries(roomsSource.rooms.entries()) });
});

app.get('/api/room/:roomId', handleVerifyApiKey, (req, res) => {
  const roomId = req.params?.roomId as string;
  const room = roomsSource.get(roomId) || null;
  res.json({ room });
});

app.get('/api/room-invite-code/:inviteCode', handleVerifyApiKey, (req, res) => {
  const inviteCode = req.params?.inviteCode as string;
  const room = roomsSource.getRoomByInviteCode(inviteCode) || null;
  res.json({ room });
});

app.get('/api/users', handleVerifyApiKey, (_req, res) => {
  res.json({ users: Object.fromEntries(usersSource.users.entries()) });
});

app.get('/api/connections', (_req, res) => {
  const activeConnections = io.sockets.sockets.size;
  const roomIds = Array.from(roomsSource.rooms).map((room) => room[0]);
  const usersIds = Array.from(usersSource.users).map((user) => user[0]);
  res.json({
    activeConnections,
    users: {
      ids: usersIds,
      length: usersIds.length,
    },
    rooms: {
      ids: roomIds,
      length: roomIds.length,
    },
  });
});

app.get('/api/public-rooms', publicRoomsHandler);

================
File: server/.eslintrc.js
================
module.exports = {
  parser: '@typescript-eslint/parser',
  extends: ['plugin:@typescript-eslint/recommended', 'plugin:prettier/recommended'],
  parserOptions: {
    ecmaVersion: 2020,
    sourceType: 'module',
  },
  env: {
    es6: true,
    node: true,
  },
  rules: {
    'no-var': 'error',
    semi: 'error',
    'no-multi-spaces': 'error',
    'space-in-parens': 'error',
    'no-multiple-empty-lines': 'error',
    'prefer-const': 'error',
  },
};

================
File: server/.gitignore
================
node_modules

================
File: server/.prettierrc
================
{
  "semi": true,
  "trailingComma": "all",
  "singleQuote": true,
  "printWidth": 150,
  "tabWidth": 2
}

================
File: server/package.json
================
{
  "name": "synkro_server",
  "version": "1.0.0",
  "description": "Server for Synkro",
  "main": "app.js",
  "scripts": {
    "start": "node ./dist/app.js",
    "dev": "nodemon src/app.ts",
    "build": "tsc -p .",
    "prettier": "prettier --write src/**/*.ts",
    "lint": "npm run prettier && eslint src/**/*.ts"
  },
  "author": "",
  "license": "ISC",
  "dependencies": {
    "cors": "^2.8.5",
    "dotenv": "^16.3.1",
    "express": "^4.18.2",
    "http-errors": "^2.0.0",
    "morgan": "^1.10.0",
    "nanoid": "^3.3.6",
    "react": "^18.2.0",
    "react-player": "^2.13.0",
    "socket.io": "^4.7.5",
    "uuid": "^9.0.0",
    "winston": "^3.9.0"
  },
  "devDependencies": {
    "@types/debug": "^4.1.8",
    "@types/express": "^4.17.17",
    "@types/http-errors": "^2.0.1",
    "@types/morgan": "^1.9.4",
    "@types/uuid": "^9.0.2",
    "@typescript-eslint/eslint-plugin": "^5.59.7",
    "eslint": "^8.41.0",
    "eslint-config-prettier": "^8.8.0",
    "eslint-plugin-prettier": "^4.2.1",
    "nodemon": "^2.0.22",
    "prettier": "^2.8.8",
    "ts-node": "^10.9.1",
    "typescript": "^5.0.4"
  }
}

================
File: server/README.md
================
# This is the server code for the Synkro Web App

Syknro is an app built to watch videos with friends in sync!

## Getting Started

First, install the needed dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

Next, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

================
File: server/tsconfig.json
================
{
  "compilerOptions": {
    "target": "es2017",
    "module": "commonjs",
    "lib": ["dom", "es6", "es2017", "esnext.asynciterable"],
    "skipLibCheck": true,
    "sourceMap": true,
    "outDir": "./dist",
    "moduleResolution": "node",
    "removeComments": true,
    "noImplicitAny": true,
    "strictNullChecks": true,
    "strictFunctionTypes": true,
    "noImplicitThis": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": false,
    "noFallthroughCasesInSwitch": true,
    "allowSyntheticDefaultImports": true,
    "esModuleInterop": true,
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "resolveJsonModule": true,
    "baseUrl": ".",
    "paths": {
      "@/*": ["./src/*"],
      "@shared/*": ["../shared/*"],
    }
  },
  "exclude": ["node_modules"],
  "include": ["./src/**/*.ts"]
}

================
File: src/components/Header/NavigationHeader.tsx
================
import Link from "next/link";
import VideoRoomHeader from "./VideoRoomHeader";
import { cn } from "@/libs/utils/frontend-utils";

export interface NavigationHeaderProps {
  page?: "home" | "video_room";
}

export const NavigationHeader: React.FC<NavigationHeaderProps> = ({ page }) => {
  return (
    <nav className="bg-card w-full flex p-3 shadow">
      <div className="flex justify-between w-full">
        <Link aria-label="Synkro (Go to home)" className="flex items-center" href="/">
          <div className="h-[2.5rem] w-[2.5rem] mr-2">
            <SynkroIcon />
          </div>
          <span className={cn("mt-[6px] font-normal text-xl text-foreground uppercase sm:block", { hidden: page === "video_room" })}>
            Synkro
          </span>
        </Link>
        {page === "video_room" && <VideoRoomHeader />}
      </div>
    </nav>
  );
};

export default NavigationHeader;

function SynkroIcon() {
  return (
    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 422 420">
      <path fill="#F583FF" d="M267.667 136H381V378H267.667z"></path>
      <path fill="#B1BEFF" d="M154.333 136h113.334v242H154.333V136z"></path>
      <path fill="#EEFEC1" d="M41 136H154.333V378H41z"></path>
      <path
        stroke="#3B4866"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="41.336"
        d="M401 117.252c0-1.02-.817-1.832-1.843-1.832H22.843A1.827 1.827 0 0021 117.252v279.597c0 1.02.817 1.832 1.843 1.832h376.314a1.827 1.827 0 001.843-1.832V117.252z"
        clipRule="evenodd"
      ></path>
      <path stroke="#3B4866" strokeLinecap="round" strokeLinejoin="round" strokeWidth="41.336" d="M306 21l-95 94.42L116 21"></path>
    </svg>
  );
}

================
File: src/components/Header/QRCodeModal.tsx
================
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { useQRCode } from "next-qrcode";

interface ModalProps {
  open: boolean;
  toggle: () => void;
  code: string;
}

export const QRCodeModal: React.FC<ModalProps> = ({ open, toggle, code }) => {
  const { Canvas } = useQRCode();

  return (
    <Dialog open={open}>
      <DialogContent className="sm:max-w-[425px]" onClose={toggle}>
        <DialogHeader>
          <DialogTitle className="text-secondary-foreground">QR Code</DialogTitle>
        </DialogHeader>
        <div className="w-full flex items-center justify-center h-[300px]">
          <Canvas
            text={code ?? ""}
            options={{
              errorCorrectionLevel: "M",
              margin: 3,
              scale: 4,
              width: 200,
              color: {
                dark: "#000000",
                light: "#FFFFFF",
              },
            }}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default QRCodeModal;

================
File: src/components/Header/VideoRoomHeader.tsx
================
import { lazy, useState } from "react";
import { QrCodeIcon, UserPlusIcon } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  TooltipProvider,
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";
import { useSocket } from "@/context/SocketContext";
import { useCopyToClipboard } from "@/hooks/useCopyToClipboard";
import { BASE_URL } from "@/constants/constants";
import { ErrorBoundary } from "@/components/ErrorBoundary";

const QRCodeModal = lazy(() =>
  import("./QRCodeModal").then((module) => {
    return { default: module.QRCodeModal };
  }),
);

const convertInviteCodeToUrl = (inviteCode: string) =>
  `${BASE_URL}/invite/${inviteCode}`;

export const VideoRoomHeader: React.FC = () => {
  const { room } = useSocket();
  const [_value, copy] = useCopyToClipboard();
  const { toast } = useToast();
  const [isQRCodeModalOpen, setIsQRCodeModalOpen] = useState<boolean>(false);

  const handleCopyInviteCode = () => {
    room?.inviteCode &&
      copy(convertInviteCodeToUrl(room.inviteCode)).then(() => {
        toast({
          variant: "info",
          description: <span>Invite link copied!</span>,
        });
      });
  };

  const handleOpenQRCodeModal = () => {
    setIsQRCodeModalOpen(!isQRCodeModalOpen);
  };

  return (
    <TooltipProvider>
      <div className="flex items-center">
        <ErrorBoundary>
          <QRCodeModal
            open={isQRCodeModalOpen}
            toggle={handleOpenQRCodeModal}
            code={
              room?.inviteCode ? convertInviteCodeToUrl(room.inviteCode) : ""
            }
          />
        </ErrorBoundary>
        <ErrorBoundary>
          <Tooltip>
            <TooltipTrigger asChild>
              <Input
                aria-label="Room Invite Code"
                className="h-10  w-32 md:w-auto rounded-l rounded-r-none cursor-pointer bg-[#342f3d6e] hover:bg-[#342f3da1] border border-r-0 border-[#614397] font-normal"
                type="text"
                onClick={handleCopyInviteCode}
                value={room?.inviteCode ?? "No invite code"}
                readOnly={true}
              />
            </TooltipTrigger>
            <TooltipContent>
              <p>Click to copy invite link</p>
            </TooltipContent>
          </Tooltip>
        </ErrorBoundary>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              aria-label="Open QR code modal"
              onClick={handleOpenQRCodeModal}
              className="w-12 h-10 rounded-r-none rounded-l-none bg-[#342f3d6e] hover:bg-[#342f3da1] border border-[#614397] border-r-0"
              variant="secondary"
            >
              <span>
                <QrCodeIcon color="#ffffff" size="1.25rem" />
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Show QR code for invite link</p>
          </TooltipContent>
        </Tooltip>
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              aria-label="Copy invite code"
              onClick={handleCopyInviteCode}
              className="w-12 h-10 rounded-r rounded-l-none bg-[#342f3d6e] hover:bg-[#342f3da1] border border-[#614397]"
              variant="secondary"
            >
              <span>
                <UserPlusIcon color="#ffffff" size="1.25rem" />
              </span>
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            <p>Copy invite link to clipboard</p>
          </TooltipContent>
        </Tooltip>
      </div>
    </TooltipProvider>
  );
};

export default VideoRoomHeader;

================
File: src/components/Modals/UserModal.tsx
================
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { KICK_USER, SET_HOST } from "@/constants/socketActions";
import { useSocket } from "@/context/SocketContext";

interface ModalProps {
  buttonText: React.ReactElement;
  headerText: React.ReactElement;
  disabled?: boolean;
  userId: string;
  open: boolean;
  handleToggle: (userId: string | null) => void;
}

export const UserModal: React.FC<ModalProps> = ({ buttonText, disabled = false, headerText, userId, open, handleToggle }) => {
  const { socket } = useSocket();

  const handleChangeAdmin = () => {
    if (userId) {
      socket?.emit(SET_HOST, userId);
      handleToggle(null);
    }
  };

  const handleKickUser = () => {
    if (userId) {
      socket?.emit(KICK_USER, userId);
      handleToggle(null);
    }
  };

  const handleOpen = () => handleToggle(userId);
  const handleClose = () => handleToggle(null);

  return (
    <Dialog open={open}>
      <DialogTrigger onClick={handleOpen} asChild disabled={disabled}>
        <button className="text-primary-foreground text-left">{buttonText}</button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]" onClose={handleClose}>
        <DialogHeader>
          <DialogTitle className="text-secondary-foreground">{headerText}</DialogTitle>
          <DialogDescription>Make changes to the room here. Click save when you&apos;re done.</DialogDescription>
        </DialogHeader>
        <DialogFooter>
          <Button className="w-full" type="submit" variant="default" onClick={handleChangeAdmin}>
            Make admin
          </Button>
        </DialogFooter>
        <DialogFooter>
          <Button className="w-full" type="submit" variant="destructive" onClick={handleKickUser}>
            Kick user
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

================
File: src/components/ui/aspect-ratio.tsx
================
import * as AspectRatioPrimitive from "@radix-ui/react-aspect-ratio"

const AspectRatio = AspectRatioPrimitive.Root

export { AspectRatio }

================
File: src/components/ui/button.tsx
================
import * as React from "react";
import { Slot } from "@radix-ui/react-slot";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/libs/utils/frontend-utils";

const buttonVariants = cva(
  "inline-flex items-center justify-center rounded-md text-xs uppercase font-medium transition-colors focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50",
  {
    variants: {
      variant: {
        default: "bg-primary text-white shadow hover:bg-primary/90",
        destructive: "bg-destructive text-destructive-foreground shadow-sm hover:bg-destructive/90",
        outline: "border border-input bg-transparent shadow-sm hover:bg-accent hover:text-accent-foreground",
        secondary: "bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80",
        ghost: "hover:bg-accent hover:text-accent-foreground",
        link: "text-primary underline-offset-4 hover:underline",
      },
      size: {
        default: "h-9 px-4 py-2",
        sm: "h-8 rounded-md px-3 text-xs",
        lg: "h-10 rounded-md px-8",
        icon: "h-9 w-9",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
    },
  }
);

export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement>, VariantProps<typeof buttonVariants> {
  asChild?: boolean;
}

const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(({ className, variant, size, asChild = false, ...props }, ref) => {
  const Comp = asChild ? Slot : "button";
  return <Comp className={cn(buttonVariants({ variant, size, className }))} ref={ref} {...props} />;
});
Button.displayName = "Button";

export { Button, buttonVariants };

================
File: src/components/ui/dialog.tsx
================
import * as React from "react";
import * as DialogPrimitive from "@radix-ui/react-dialog";
import { Cross2Icon } from "@radix-ui/react-icons";

import { cn } from "@/libs/utils/frontend-utils";

const Dialog = DialogPrimitive.Root;

const DialogTrigger = DialogPrimitive.Trigger;

const DialogPortal = ({ ...props }: DialogPrimitive.DialogPortalProps) => {
  return <DialogPrimitive.Portal {...props} />;
};
DialogPortal.displayName = DialogPrimitive.Portal.displayName;

const DialogOverlay = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Overlay> & { onClose: () => void }
>(({ className, onClose, ...props }, ref) => (
  <DialogPrimitive.Overlay
    ref={ref}
    className={cn(
      "fixed inset-0 z-50 bg-background/80 backdrop-blur-sm data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",
      className
    )}
    onClick={onClose}
    {...props}
  />
));
DialogOverlay.displayName = DialogPrimitive.Overlay.displayName;

const DialogContent = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Content> & { onClose: () => void }
>(({ className, children, onClose, ...props }, ref) => {
  return (
    <DialogPortal>
      <DialogOverlay onClose={onClose} />
      <DialogPrimitive.Content
        ref={ref}
        className={cn(
          "fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg md:w-full",
          className
        )}
        {...props}
      >
        {children}
        <DialogPrimitive.Close
          onClick={onClose}
          className="absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background text-gray-400 transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground"
        >
          <Cross2Icon className="h-4 w-4" />
          <span className="sr-only">Close</span>
        </DialogPrimitive.Close>
      </DialogPrimitive.Content>
    </DialogPortal>
  );
});
DialogContent.displayName = DialogPrimitive.Content.displayName;

const DialogHeader = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("flex flex-col space-y-1.5", className)} {...props} />
);
DialogHeader.displayName = "DialogHeader";

const DialogFooter = ({ className, ...props }: React.HTMLAttributes<HTMLDivElement>) => (
  <div className={cn("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2", className)} {...props} />
);
DialogFooter.displayName = "DialogFooter";

const DialogTitle = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Title>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Title ref={ref} className={cn("text-lg font-semibold leading-none tracking-tight", className)} {...props} />
));
DialogTitle.displayName = DialogPrimitive.Title.displayName;

const DialogDescription = React.forwardRef<
  React.ElementRef<typeof DialogPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof DialogPrimitive.Description>
>(({ className, ...props }, ref) => (
  <DialogPrimitive.Description ref={ref} className={cn("text-sm text-muted-foreground", className)} {...props} />
));
DialogDescription.displayName = DialogPrimitive.Description.displayName;

export { Dialog, DialogTrigger, DialogContent, DialogHeader, DialogFooter, DialogTitle, DialogDescription };

================
File: src/components/ui/input.tsx
================
import * as React from "react";

import { cn } from "@/libs/utils/frontend-utils";

export interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(({ className, type, ...props }, ref) => {
  return (
    <input
      type={type}
      className={cn(
        "flex h-9 w-full rounded-md border border-input bg-transparent text-foreground px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:border-primary disabled:cursor-not-allowed disabled:opacity-50",
        className
      )}
      ref={ref}
      {...props}
    />
  );
});
Input.displayName = "Input";

export { Input };

================
File: src/components/ui/label.tsx
================
import * as React from "react";
import * as LabelPrimitive from "@radix-ui/react-label";
import { cva, type VariantProps } from "class-variance-authority";

import { cn } from "@/libs/utils/frontend-utils";

const labelVariants = cva("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70");

const Label = React.forwardRef<
  React.ElementRef<typeof LabelPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> & VariantProps<typeof labelVariants>
>(({ className, ...props }, ref) => <LabelPrimitive.Root ref={ref} className={cn(labelVariants(), className)} {...props} />);
Label.displayName = LabelPrimitive.Root.displayName;

export { Label };

================
File: src/components/ui/switch.tsx
================
import * as React from "react";
import * as SwitchPrimitives from "@radix-ui/react-switch";

import { cn } from "@/libs/utils/frontend-utils";

const Switch = React.forwardRef<
  React.ElementRef<typeof SwitchPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof SwitchPrimitives.Root>
>(({ className, ...props }, ref) => (
  <SwitchPrimitives.Root
    className={cn(
      "peer inline-flex h-[20px] w-[36px] shrink-0 cursor-pointer items-center rounded-full border-2 border-transparent shadow-sm transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:ring-offset-background disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=unchecked]:bg-input",
      className
    )}
    {...props}
    ref={ref}
  >
    <SwitchPrimitives.Thumb
      className={cn(
        "pointer-events-none block h-4 w-4 rounded-full bg-background shadow-lg ring-0 transition-transform data-[state=checked]:translate-x-4 data-[state=unchecked]:translate-x-0"
      )}
    />
  </SwitchPrimitives.Root>
));
Switch.displayName = SwitchPrimitives.Root.displayName;

export { Switch };

================
File: src/components/ui/toast.tsx
================
import * as React from "react";
import { Cross2Icon } from "@radix-ui/react-icons";
import * as ToastPrimitives from "@radix-ui/react-toast";
import { cva, type VariantProps } from "class-variance-authority";
import { cn } from "@/libs/utils/frontend-utils";

const ToastProvider = ToastPrimitives.Provider;

const ToastViewport = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Viewport>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Viewport>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Viewport
    ref={ref}
    className={cn(
      "fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px] gap-y-2",
      className
    )}
    {...props}
  />
));
ToastViewport.displayName = ToastPrimitives.Viewport.displayName;

const toastVariants = cva(
  "group pointer-events-auto relative flex w-full items-center justify-between overflow-hidden rounded-md border p-4 pr-6 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",
  {
    variants: {
      variant: {
        alert: "border border-orange-600 bg-[#67340f] text-secondary-foreground",
        info: "border border-blue-500 bg-[#2b3b5d] text-secondary-foreground",
        default: "border bg-background text-primary-foreground",
        success: "border border-green-600 bg-[#163121] text-secondary-foreground",
        destructive: "destructive group border border-destructive bg-[#471b1b] text-destructive-foreground",
      },
    },
    defaultVariants: {
      variant: "default",
    },
  }
);

const Toast = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Root>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Root> & VariantProps<typeof toastVariants>
>(({ className, variant, ...props }, ref) => {
  return <ToastPrimitives.Root ref={ref} className={cn(toastVariants({ variant }), className)} {...props} />;
});
Toast.displayName = ToastPrimitives.Root.displayName;

const ToastAction = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Action>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Action>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Action
    ref={ref}
    className={cn(
      "inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium transition-colors hover:bg-secondary focus:outline-none focus:ring-1 focus:ring-ring disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",
      className
    )}
    {...props}
  />
));
ToastAction.displayName = ToastPrimitives.Action.displayName;

const ToastClose = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Close>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Close>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Close
    ref={ref}
    className={cn(
      "absolute right-1 top-1 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-1 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",
      className
    )}
    toast-close=""
    {...props}
  >
    <Cross2Icon className="h-4 w-4" />
  </ToastPrimitives.Close>
));
ToastClose.displayName = ToastPrimitives.Close.displayName;

const ToastTitle = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Title>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Title>
>(({ className, ...props }, ref) => (
  <ToastPrimitives.Title ref={ref} className={cn("text-sm font-semibold [&+div]:text-xs", className)} {...props} />
));
ToastTitle.displayName = ToastPrimitives.Title.displayName;

const ToastDescription = React.forwardRef<
  React.ElementRef<typeof ToastPrimitives.Description>,
  React.ComponentPropsWithoutRef<typeof ToastPrimitives.Description>
>(({ className, ...props }, ref) => <ToastPrimitives.Description ref={ref} className={cn("text-sm opacity-90", className)} {...props} />);
ToastDescription.displayName = ToastPrimitives.Description.displayName;

type ToastProps = React.ComponentPropsWithoutRef<typeof Toast>;

type ToastActionElement = React.ReactElement<typeof ToastAction>;

export {
  type ToastProps,
  type ToastActionElement,
  ToastProvider,
  ToastViewport,
  Toast,
  ToastTitle,
  ToastDescription,
  ToastClose,
  ToastAction,
};

================
File: src/components/ui/toaster.tsx
================
import { InfoIcon } from "lucide-react";
import { Toast, ToastClose, ToastDescription, ToastProvider, ToastTitle, ToastViewport } from "@/components/ui/toast";
import { useToast } from "@/components/ui/use-toast";

export function Toaster() {
  const { toasts } = useToast();

  return (
    <ToastProvider>
      {toasts.map(function ({ id, title, description, action, Icon, iconClassname, ...props }) {
        return (
          <Toast key={id} {...props}>
            {Icon ? (
              <span className={iconClassname ? `mr-4 ${iconClassname}` : "mr-4"}>
                <Icon />
              </span>
            ) : props.variant === "info" ? (
              <span className={iconClassname ? `mr-4 ${iconClassname}` : "mr-4"}>
                <InfoIcon />
              </span>
            ) : null}
            <div className="grid gap-1 mr-auto">
              {title && <ToastTitle>{title}</ToastTitle>}
              {description && <ToastDescription>{description}</ToastDescription>}
            </div>
            {action}
            <ToastClose />
          </Toast>
        );
      })}
      <ToastViewport />
    </ToastProvider>
  );
}

================
File: src/components/ui/tooltip.tsx
================
import * as React from "react";
import * as TooltipPrimitive from "@radix-ui/react-tooltip";

import { cn } from "@/libs/utils/frontend-utils";

const TooltipProvider = TooltipPrimitive.Provider;

const Tooltip = TooltipPrimitive.Root;

const TooltipTrigger = TooltipPrimitive.Trigger;

const TooltipContent = React.forwardRef<
  React.ElementRef<typeof TooltipPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof TooltipPrimitive.Content>
>(({ className, sideOffset = 4, ...props }, ref) => (
  <TooltipPrimitive.Portal>
    <TooltipPrimitive.Content
      ref={ref}
      sideOffset={sideOffset}
      className={cn(
        "z-50 overflow-hidden rounded-md bg-black/80 shadow-sm backdrop-blur-lg border px-3 py-1 text-xs text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
        className,
      )}
      {...props}
    />
  </TooltipPrimitive.Portal>
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

export { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider };

================
File: src/components/ui/use-toast.ts
================
// Inspired by react-hot-toast library
import * as React from "react";

import type { ToastActionElement, ToastProps } from "@/components/ui/toast";

const TOAST_LIMIT = 4;
const TOAST_REMOVE_DELAY = 1000000;

type ToasterToast = ToastProps & {
  id: string;
  title?: React.ReactNode;
  description?: React.ReactNode;
  action?: ToastActionElement;
  Icon?: React.ElementType;
  iconClassname?: string;
};

const actionTypes = {
  ADD_TOAST: "ADD_TOAST",
  UPDATE_TOAST: "UPDATE_TOAST",
  DISMISS_TOAST: "DISMISS_TOAST",
  REMOVE_TOAST: "REMOVE_TOAST",
} as const;

let count = 0;

function genId() {
  count = (count + 1) % Number.MAX_VALUE;
  return count.toString();
}

type ActionType = typeof actionTypes;

type Action =
  | {
      type: ActionType["ADD_TOAST"];
      toast: ToasterToast;
    }
  | {
      type: ActionType["UPDATE_TOAST"];
      toast: Partial<ToasterToast>;
    }
  | {
      type: ActionType["DISMISS_TOAST"];
      toastId?: ToasterToast["id"];
    }
  | {
      type: ActionType["REMOVE_TOAST"];
      toastId?: ToasterToast["id"];
    };

interface State {
  toasts: ToasterToast[];
}

const toastTimeouts = new Map<string, ReturnType<typeof setTimeout>>();

const addToRemoveQueue = (toastId: string) => {
  if (toastTimeouts.has(toastId)) {
    return;
  }

  const timeout = setTimeout(() => {
    toastTimeouts.delete(toastId);
    dispatch({
      type: "REMOVE_TOAST",
      toastId: toastId,
    });
  }, TOAST_REMOVE_DELAY);

  toastTimeouts.set(toastId, timeout);
};

export const reducer = (state: State, action: Action): State => {
  switch (action.type) {
    case "ADD_TOAST":
      return {
        ...state,
        toasts: [action.toast, ...state.toasts].slice(0, TOAST_LIMIT),
      };

    case "UPDATE_TOAST":
      return {
        ...state,
        toasts: state.toasts.map((t) => (t.id === action.toast.id ? { ...t, ...action.toast } : t)),
      };

    case "DISMISS_TOAST": {
      const { toastId } = action;

      // ! Side effects ! - This could be extracted into a dismissToast() action,
      // but I'll keep it here for simplicity
      if (toastId) {
        addToRemoveQueue(toastId);
      } else {
        state.toasts.forEach((toast) => {
          addToRemoveQueue(toast.id);
        });
      }

      return {
        ...state,
        toasts: state.toasts.map((t) =>
          t.id === toastId || toastId === undefined
            ? {
                ...t,
                open: false,
              }
            : t
        ),
      };
    }
    case "REMOVE_TOAST":
      if (action.toastId === undefined) {
        return {
          ...state,
          toasts: [],
        };
      }
      return {
        ...state,
        toasts: state.toasts.filter((t) => t.id !== action.toastId),
      };
  }
};

const listeners: Array<(state: State) => void> = [];

let memoryState: State = { toasts: [] };

function dispatch(action: Action) {
  memoryState = reducer(memoryState, action);
  listeners.forEach((listener) => {
    listener(memoryState);
  });
}

type Toast = Omit<ToasterToast, "id">;

function toast({ ...props }: Toast) {
  const id = genId();

  const update = (props: ToasterToast) =>
    dispatch({
      type: "UPDATE_TOAST",
      toast: { ...props, id },
    });
  const dismiss = () => dispatch({ type: "DISMISS_TOAST", toastId: id });

  dispatch({
    type: "ADD_TOAST",
    toast: {
      ...props,
      id,
      open: true,
      onOpenChange: (open) => {
        if (!open) dismiss();
      },
    },
  });

  return {
    id: id,
    dismiss,
    update,
  };
}

function useToast() {
  const [state, setState] = React.useState<State>(memoryState);

  React.useEffect(() => {
    listeners.push(setState);
    return () => {
      const index = listeners.indexOf(setState);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    };
  }, [state]);

  return {
    ...state,
    toast,
    dismiss: (toastId?: string) => dispatch({ type: "DISMISS_TOAST", toastId }),
  };
}

export { useToast, toast };

================
File: src/components/VideoRoom/Chat.tsx
================
import React, { useEffect, useRef, useState } from "react";

import { ArrowBigDown, SendIcon } from "lucide-react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { ChatMessage, Messages } from "@/types/interfaces";
import { USER_MESSAGE } from "@/constants/socketActions";
import { generateUserIcon, getMessageClassname } from "@/libs/utils/chat";
import { useSocket } from "@/context/SocketContext";

interface ChatProps {
  messages: Messages;
  roomId: string;
}

const Chat: React.FC<ChatProps> = ({ messages, roomId }) => {
  const [chatMessage, setChatMessage] = useState<string>("");
  const [isNewMessagePopupShown, setIsNewMessagePopupShown] = useState<boolean>(false);
  const chatContainerRef = useRef<HTMLDivElement & { maxScrollTop?: number }>(null);

  const { toast } = useToast();
  const { socket, room } = useSocket();

  const scrollToBottom = (force = false, smooth = true) => {
    const chatContainer = chatContainerRef.current;

    if (!chatContainer) return;

    const { scrollHeight, scrollTop, offsetHeight } = chatContainer;
    chatContainer.maxScrollTop = scrollHeight - offsetHeight;

    if (chatContainer.maxScrollTop - scrollTop <= offsetHeight || force) {
      if (smooth) {
        chatContainer.scrollTo({
          top: scrollHeight,
          left: 0,
          behavior: "smooth",
        });
      } else {
        chatContainer.scroll(0, scrollHeight);
      }
    } else setIsNewMessagePopupShown(true);
  };

  useEffect(() => scrollToBottom(true, false), []);

  useEffect(() => {
    const lastMessage = (messages as ChatMessage[])[messages.length - 1];
    if (lastMessage?.userId !== socket?.data?.userId) {
      scrollToBottom();
    } else {
      scrollToBottom(true);
    }
  }, [messages]);

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    const { scrollHeight, offsetHeight, scrollTop } = chatContainer;

    if (scrollTop === scrollHeight - offsetHeight && isNewMessagePopupShown) {
      setIsNewMessagePopupShown(false);
    }
  }, [isNewMessagePopupShown]);

  useEffect(() => {
    const chatContainer = chatContainerRef.current;
    if (!chatContainer) return;

    const userScrolledToEnd = () => {
      const { scrollHeight, offsetHeight, scrollTop } = chatContainer;
      const containerHeight = scrollHeight - offsetHeight;

      if (containerHeight <= scrollTop && isNewMessagePopupShown) {
        setIsNewMessagePopupShown(false);
      }
    };

    chatContainer.addEventListener("scroll", userScrolledToEnd, false);

    return () => chatContainer.removeEventListener("scroll", userScrolledToEnd, false);
  }, [isNewMessagePopupShown]);

  const handleSeeNewMessages = () => {
    scrollToBottom(true);
    setIsNewMessagePopupShown(false);
  };

  const handleChangeMessage = (e: React.ChangeEvent<HTMLInputElement>) => {
    setChatMessage(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleSendMessage();
  };

  const handleSendMessage = () => {
    if (!socket || chatMessage.trim() === "") return;
    if (chatMessage.length > 500) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Message length cannot be greater than 500",
      });
      return;
    }
    socket.emit(USER_MESSAGE, chatMessage, roomId);
    setChatMessage("");
  };

  return (
    <div className="flex flex-col flex-grow w-full h-full relative">
      <div className="flex-grow overflow-y-auto p-4 gap-y-4 flex flex-col chat-scrollbar" ref={chatContainerRef}>
        {messages.map((message, index) => {
          const userIcon = message.type === "USER" && room?.host && generateUserIcon(message?.userId, room.host, message?.isAdmin);
          return (
            <div
              className={`${getMessageClassname(
                message.type === "USER" && message?.isAdmin ? "ADMIN" : message.type
              )} break-words rounded p-1 px-2`}
              key={index}
            >
              {message.type === "USER" && (
                <div
                  className={`${message.userId === socket?.data?.userId ? "text-red-500" : "text-green-500"}`}
                  style={{ color: message?.isAdmin ? "#ff45ef" : message.userId === socket?.data?.userId ? undefined : message.color }}
                >
                  {userIcon && <span className="mr-1">{userIcon}</span>}
                  {message.username}
                </div>
              )}
              <p className="text-sm text-inherit">{message.message}</p>
            </div>
          );
        })}
      </div>
      {isNewMessagePopupShown && (
        <div className="cursor-pointer absolute w-full bottom-[40.25px]" onClick={handleSeeNewMessages}>
          <NewMessage />
        </div>
      )}
      <div className="flex items-center p-2">
        <Input
          className="h-10 rounded-l rounded-r-none"
          type="text"
          value={chatMessage}
          onChange={handleChangeMessage}
          onKeyDown={handleKeyDown}
          placeholder="Say something"
          maxLength={500}
        />
        <Button aria-label="Send message" onClick={handleSendMessage} className="w-12 h-10 rounded-r rounded-l-none">
          <span>
            <SendIcon color="#ffffff" size="1.25rem" />
          </span>
        </Button>
      </div>
    </div>
  );
};

const NewMessage = () => {
  return (
    <div className="group bg-indigo-700 hover:bg-indigo-600 text-center py-2 lg:px-4 relative top-[-1rem]">
      <div
        className="p-1 bg-indigo-600 group-hover:bg-indigo-500 items-center text-indigo-100 leading-none lg:rounded-full flex lg:inline-flex w-full"
        role="alert"
      >
        <span className="flex rounded-full bg-indigo-400 uppercase px-2 text-xs font-bold mr-3">
          <ArrowBigDown fill="#FFFFFF" strokeOpacity={0} />
        </span>
        <span className="font-semibold mr-2 text-left flex-auto">New message!</span>
        <svg className="fill-current opacity-75 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
          <path d="M12.95 10.707l.707-.707L8 4.343 6.586 5.757 10.828 10l-4.242 4.243L8 15.657l4.95-4.95z" />
        </svg>
      </div>
    </div>
  );
};

export default Chat;

================
File: src/components/VideoRoom/PlayPauseButton.tsx
================
import { PauseIcon, PlayIcon } from "lucide-react";
import { ButtonActions } from "./RoomToolbar";
import { Button } from "@/components/ui/button";
import {
  Tooltip,
  TooltipTrigger,
  TooltipContent,
} from "@/components/ui/tooltip";

interface PlayPauseButtonProps {
  onClick: (newActiveButton: ButtonActions) => void;
  isPlaying: boolean;
}

export const PlayPauseButton: React.FC<PlayPauseButtonProps> = ({
  onClick,
  isPlaying,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          aria-label={isPlaying ? "Pause video" : "Play video"}
          className="w-9 h-9"
          onClick={() => onClick(isPlaying ? "pause" : "play")}
          variant="secondary"
        >
          <span>
            {isPlaying ? (
              <PauseIcon color="#FFFFFF" size="1.25rem" />
            ) : (
              <PlayIcon color="#FFFFFF" size="1.25rem" />
            )}
          </span>
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{isPlaying ? "Pause" : "Play"}</p>
      </TooltipContent>
    </Tooltip>
  );
};

================
File: src/components/VideoRoom/Queue.tsx
================
import React, { useState } from "react";

import { PlayIcon, PlusIcon, Trash2Icon } from "lucide-react";
import {
  DragDropContext,
  Droppable,
  Draggable,
  DropResult,
} from "@hello-pangea/dnd";
import { UAParser } from "ua-parser-js";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { VideoQueueItem } from "@/types/interfaces";
import {
  ADD_VIDEO_TO_QUEUE,
  REMOVE_VIDEO_FROM_QUEUE,
  VIDEO_QUEUE_REORDERED,
  VIDEO_QUEUE_CLEARED,
} from "@/constants/socketActions";

import { Queue as QueueType } from "@/hooks/useQueue";
import { useToast } from "@/components/ui/use-toast";
import Image from "next/image";
import { useSocket } from "@/context/SocketContext";
import { runIfAuthorized } from "@/libs/utils/socket";
import { ButtonActions } from "./RoomToolbar";
import ReactPlayer from "react-player";
import { fetchMediaData } from "@/libs/utils/video-fetch-lib";
import { convertURLToYoutubeVideoId } from "@/libs/utils/frontend-utils";

interface QueueProps {
  currentVideoId: string;
  videoQueue: QueueType<VideoQueueItem>;
  onClickPlayerButton: (
    newActiveButton: ButtonActions,
    payload?: { videoUrl: string; videoIndex?: number },
  ) => void;
}

const Queue: React.FC<QueueProps> = ({ videoQueue, onClickPlayerButton }) => {
  const [newVideoInQueueUrl, setNewVideoInQueueUrl] = useState<string>("");

  const { toast } = useToast();
  const { socket, room } = useSocket();

  const isAuthorized =
    socket?.data.isAdmin || room?.host === socket?.data?.userId;
  const isMobile = new UAParser().getDevice().type === "mobile";

  const getIndexOfVideoInQueue = (videoId: string): number => {
    const index = videoQueue.queue.findIndex((video) => video.id === videoId);
    return index;
  };

  const handleChangeVideoUrl = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewVideoInQueueUrl(e.target.value);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleAddVideoToQueue();
  };

  const handleAddVideoToQueue = async () => {
    if (!isAuthorized || !socket) return;
    if (!ReactPlayer.canPlay(newVideoInQueueUrl)) return;

    const removeQueryParams = (url: string, param: string): string => {
      if (!convertURLToYoutubeVideoId(url)) return url;
      const parsedUrl = new URL(url);
      const params = parsedUrl.searchParams;
      params.delete(param);
      return parsedUrl.toString();
    };

    const formattedVideoUrl = removeQueryParams(newVideoInQueueUrl, "t");
    const videoExist = videoQueue.queue.find(
      (video) => video.url === formattedVideoUrl,
    );

    if (!!videoExist) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "This video is already in the current queue",
      });
      return;
    }

    const newVideo = await fetchMediaData(formattedVideoUrl);

    if (newVideo?.url) {
      videoQueue.add(newVideo);
      socket.emit(ADD_VIDEO_TO_QUEUE, newVideo);
      setNewVideoInQueueUrl("");
    } else {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description:
          "This URL seems to be invalid or is not from an accepted provider",
      });
    }
  };

  const handleChangeVideo = (newVideoUrl: string, newVideoId: string) => {
    if (socket?.data.isAdmin && room) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          const newVideoIndex = getIndexOfVideoInQueue(newVideoId);
          onClickPlayerButton("change-video", {
            videoUrl: newVideoUrl,
            videoIndex: newVideoIndex,
          });
        },
        socket.data.isAdmin,
      );
    }
  };

  const handleRemoveVideoFromQueue = (videoUrl: string) => {
    if (socket?.data.isAdmin && room) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          socket.emit(REMOVE_VIDEO_FROM_QUEUE, videoUrl);
          videoQueue.removeItem("url", videoUrl);
        },
        socket.data.isAdmin,
      );
    }
  };

  const handleClearQueue = () => {
    if (socket?.data.isAdmin && room) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          if (videoQueue.queue.length > 0) {
            socket.emit(VIDEO_QUEUE_CLEARED);
            videoQueue.clear();
          }
        },
        socket.data.isAdmin,
      );
    }
  };

  const handleReorder = (
    list: VideoQueueItem[],
    startIndex: number,
    endIndex: number,
  ): VideoQueueItem[] => {
    const result = Array.from(list);
    const [removed] = result.splice(startIndex, 1);
    result.splice(endIndex, 0, removed);
    return result;
  };

  const onDragEnd = (result: DropResult) => {
    if (socket?.data.isAdmin && room) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          if (!result.destination) return;
          const items = handleReorder(
            videoQueue.queue,
            result.source.index,
            result.destination.index,
          );
          videoQueue.set(items);
          socket.emit(VIDEO_QUEUE_REORDERED, items);
        },
        socket.data.isAdmin,
      );
    }
  };

  const getItemStyle = (draggableStyle?: React.CSSProperties) => ({
    userSelect: "none" as React.CSSProperties["userSelect"],
    ...draggableStyle,
  });

  // inset 0 2px 3px rgba(255, 255, 255, 0.3), inset 0 -2px 3px #4d219b, 0 1px 1px #331567
  // shadow-[inset_0_2px_3px_rgba(255,_255,_255,_0.3),_inset_0_-2px_3px_#4d219b,_0_1px_1px_#331567]

  // background: linear-gradient(90deg, rgba(2,0,36,1) 0%, rgba(107,46,215,1) 35%, rgba(77,52,122,1) 100%);
  // bg-[linear-gradient(90deg,_rgba(2,0,36,1)_0%,_rgba(107,46,215,1)_35%,rgba(77,52,122,1)_100%)]

  //bg-[linear-gradient(120deg,_rgba(2,0,36,1)_0%,_rgba(107,46,215,1)_35%,rgba(77,52,122,1)_100%)]

  return (
    <div className="flex flex-col flex-grow w-full h-full relative hide-scrollbar">
      <div className="flex items-center p-2">
        <Input
          className="h-10 rounded-l rounded-r-none"
          type="text"
          value={newVideoInQueueUrl}
          onChange={handleChangeVideoUrl}
          onKeyDown={handleKeyDown}
          placeholder={
            !isAuthorized ? "Only the host can add videos" : "Add video"
          }
          disabled={!isAuthorized}
        />
        <Button
          onClick={handleAddVideoToQueue}
          className="w-12 h-10 rounded-r rounded-l-none"
          disabled={!isAuthorized}
        >
          <span>
            <PlusIcon color="#FFFFFF" size="1.25rem" />
          </span>
        </Button>
      </div>

      <DragDropContext onDragEnd={onDragEnd}>
        <Droppable
          droppableId="droppable"
          isDropDisabled={!isAuthorized || isMobile}
        >
          {(provided) => (
            <div
              className="flex-grow overflow-y-auto p-4 h-full gap-y-4 flex flex-col"
              {...provided.droppableProps}
              ref={provided.innerRef}
            >
              {videoQueue.queue.map((video, index) => (
                <Draggable
                  key={`${video.id}-${index}`}
                  draggableId={`${video.id}-${index}`}
                  index={index}
                  isDragDisabled={!isAuthorized || isMobile}
                >
                  {(provided, snapshot) => (
                    <div
                      className={`${
                        room?.videoInfo.currentQueueIndex === index
                          ? "bg-gradient-to-br from-[#4d347a] from-10% via-[#6b2ed7] via-60% to-[#18118d] to-92%"
                          : snapshot.isDragging
                            ? "bg-[#6936ff75]"
                            : "bg-[#212123]"
                      } rounded w-[250px] md:w-auto p-2 cursor-pointer`}
                      ref={provided.innerRef}
                      {...provided.draggableProps}
                      {...provided.dragHandleProps}
                      style={getItemStyle(provided.draggableProps.style)}
                    >
                      <div className="w-full h-[130px] relative">
                        {isAuthorized && (
                          <div className="flex absolute bottom-2 right-2 z-[2] gap-2">
                            <Button
                              aria-label="Play video"
                              className="p-2 w-8 h-8 bg-black"
                              onClick={() =>
                                handleChangeVideo(video.url, video.id)
                              }
                            >
                              <span>
                                <PlayIcon
                                  fill="#FFFFFF"
                                  color="#FFFFFF"
                                  size="1.25rem"
                                />
                              </span>
                            </Button>
                            <Button
                              aria-label="Remove video"
                              className="p-2 w-8 h-8 bg-black"
                              onClick={() =>
                                handleRemoveVideoFromQueue(video.url)
                              }
                            >
                              <span>
                                <Trash2Icon color="#FFFFFF" size="1.25rem" />
                              </span>
                            </Button>
                          </div>
                        )}
                        <Image
                          alt=""
                          src={
                            video.thumbnail ||
                            "/next-assets/images/synkro_placeholder.svg"
                          }
                          fill={true}
                          quality={25}
                        />
                      </div>
                      <p className="text-primary-foreground mt-2 text-sm">
                        {video.title}
                      </p>
                    </div>
                  )}
                </Draggable>
              ))}
            </div>
          )}
        </Droppable>
      </DragDropContext>

      {isAuthorized && (
        <div className="w-full flex p-2">
          <Button
            onClick={handleClearQueue}
            className="w-full rounded"
            disabled={!isAuthorized}
            variant="destructive"
          >
            <span className="uppercase">Clear queue</span>
          </Button>
        </div>
      )}
    </div>
  );
};

export default Queue;

================
File: src/components/VideoRoom/RoomToolbar.tsx
================
import { useState } from "react";
import {
  ArrowRightIcon,
  RefreshCwIcon,
  FastForwardIcon,
  ListOrderedIcon,
  MessageSquareIcon,
  RewindIcon,
  SettingsIcon,
  DoorOpenIcon,
} from "lucide-react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import {
  TooltipProvider,
  TooltipTrigger,
  TooltipContent,
  Tooltip,
} from "@/components/ui/tooltip";

import { runIfAuthorized } from "@/libs/utils/socket";
import { useSocket } from "@/context/SocketContext";

import { Separator } from "../Separator";
import { PlayPauseButton } from "./PlayPauseButton";
import ReactPlayer from "react-player";

export const defaultButtonClassName = "w-9 h-9 min-w-[2.25rem]";

export type ButtonActions =
  | SidebarViews
  | "expand"
  | "play"
  | "pause"
  | "fast-forward"
  | "rewind"
  | "sync-video"
  | "change-video"
  | "leave-room";

export type SidebarViews = "chat" | "queue" | "settings";

interface ButtonWithTooltipProps {
  tooltipText: string;
  children: React.ReactNode;
  className?: string;
  onClick?: () => void;
  variant?:
    | "default"
    | "destructive"
    | "outline"
    | "secondary"
    | "ghost"
    | "link";
  "aria-label": string;
}

const ButtonWithTooltip: React.FC<ButtonWithTooltipProps> = ({
  tooltipText,
  children,
  className,
  onClick,
  variant,
  "aria-label": ariaLabel,
}) => {
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          aria-label={ariaLabel}
          className={className}
          onClick={onClick}
          variant={variant}
        >
          {children}
        </Button>
      </TooltipTrigger>
      <TooltipContent>
        <p>{tooltipText}</p>
      </TooltipContent>
    </Tooltip>
  );
};

interface RoomToolbarProps {
  activeView: ButtonActions;
  onClickPlayerButton: (
    newActiveButton: ButtonActions,
    payload?: { videoUrl: string; videoIndex?: number },
  ) => void;
  isPlaying: boolean;
  roomId: string;
}

export const RoomToolbar: React.FC<RoomToolbarProps> = ({
  activeView,
  onClickPlayerButton,
  isPlaying,
}) => {
  const [newVideoUrl, setNewVideoUrl] = useState<string>("");

  const { socket, room } = useSocket();
  const { toast } = useToast();

  const handleChangeNewVideoUrl = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setNewVideoUrl(value);
  };

  const handleChangeVideo = () => {
    if (newVideoUrl.trim() === "") return;
    if (!ReactPlayer.canPlay(newVideoUrl)) {
      toast({
        variant: "destructive",
        title: "Incorrect video URL",
        description: "Sorry, this video cannot be played",
      });
      console.error("Video URL cannot be played:", newVideoUrl);
      return;
    }
    if (socket?.data.isAdmin && room?.host) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          onClickPlayerButton("change-video", { videoUrl: newVideoUrl });
          setNewVideoUrl("");
        },
        socket.data.isAdmin,
      );
    }
  };

  const handleChangeVideoOnKeyDown = (
    e: React.KeyboardEvent<HTMLInputElement>,
  ) => {
    if (e.key === "Enter") handleChangeVideo();
  };

  const handleSyncVideo = () => {
    if (socket?.data.isAdmin && room?.host) {
      if (room.host !== socket.data.userId) {
        onClickPlayerButton("sync-video", { videoUrl: newVideoUrl });
      } else {
        runIfAuthorized(
          room.host,
          socket.data.userId,
          () => {},
          socket.data.isAdmin,
        );
      }
    }
  };

  const isAuthorized =
    socket?.data.isAdmin || room?.host === socket?.data?.userId;

  return (
    <TooltipProvider>
      <div className="max-w-[80rem] w-full bg-card shadow-md p-2.5 rounded flex">
        <div className="w-full">
          <div className="w-full flex gap-2 overflow-x-auto h-9">
            <SidebarViewButtons
              activeView={activeView}
              className={defaultButtonClassName}
              onClick={onClickPlayerButton}
            />
            <Separator />
            <PlayPauseButton
              onClick={onClickPlayerButton}
              isPlaying={isPlaying}
            />
            <ButtonWithTooltip
              tooltipText="Rewind 10 seconds"
              aria-label="Rewind video"
              className={defaultButtonClassName}
              onClick={() => onClickPlayerButton("rewind")}
              variant="secondary"
            >
              <span>
                <RewindIcon color="#FFFFFF" size="1.25rem" />
              </span>
            </ButtonWithTooltip>
            <ButtonWithTooltip
              tooltipText="Fast forward 10 seconds"
              aria-label="Fast forward video"
              className={defaultButtonClassName}
              onClick={() => onClickPlayerButton("fast-forward")}
              variant="secondary"
            >
              <span>
                <FastForwardIcon color="#FFFFFF" size="1.25rem" />
              </span>
            </ButtonWithTooltip>
            <Separator className="hidden min-[1020px]:flex" />
            <ButtonWithTooltip
              tooltipText="Sync video with host"
              aria-label="Sync video with host"
              className="rounded w-9 h-9"
              onClick={handleSyncVideo}
            >
              <span>
                <RefreshCwIcon color="#FFFFFF" size="1.25rem" />
              </span>
            </ButtonWithTooltip>
            <div className="w-full items-center hidden min-[1020px]:flex">
              <Input
                className="min-w-[140px]"
                disabled={!isAuthorized}
                type="text"
                placeholder={
                  !isAuthorized
                    ? "Only the host can change the video"
                    : "Change video"
                }
                onChange={handleChangeNewVideoUrl}
                onKeyDown={handleChangeVideoOnKeyDown}
                value={newVideoUrl}
              />
              <ButtonWithTooltip
                tooltipText="Change current video"
                aria-label="Change video"
                className="ml-2 rounded w-12"
                onClick={handleChangeVideo}
              >
                <span>
                  <ArrowRightIcon color="#FFFFFF" size="1.25rem" />
                </span>
              </ButtonWithTooltip>
            </div>
            <Separator />
            <ButtonWithTooltip
              tooltipText="Leave this room"
              aria-label="Leave room"
              className={`${defaultButtonClassName} ml-auto bg-red-600 hover:bg-red-500`}
              onClick={() => onClickPlayerButton("leave-room")}
              variant="secondary"
            >
              <span>
                <DoorOpenIcon color="#FFFFFF" size="1.25rem" />
              </span>
            </ButtonWithTooltip>
          </div>
          <div className="mt-3 w-full flex min-[1020px]:hidden">
            <div className="w-full flex items-center">
              <Input
                className="min-w-[140px]"
                disabled={!isAuthorized}
                type="text"
                placeholder={
                  !isAuthorized
                    ? "Only the host can change the video"
                    : "Change video"
                }
                onChange={handleChangeNewVideoUrl}
                onKeyDown={handleChangeVideoOnKeyDown}
                value={newVideoUrl}
              />
              <ButtonWithTooltip
                tooltipText="Change current video"
                aria-label="Change video"
                className="ml-2 rounded w-12"
                onClick={handleChangeVideo}
              >
                <span>
                  <ArrowRightIcon color="#FFFFFF" size="1.25rem" />
                </span>
              </ButtonWithTooltip>
            </div>
          </div>
        </div>
      </div>
    </TooltipProvider>
  );
};

export const SidebarViewButtons = ({
  className,
  activeView,
  onClick,
}: {
  className: string;
  activeView: ButtonActions;
  onClick: (newActiveView: SidebarViews) => void;
}) => {
  return (
    <>
      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="Go to chat view"
            className={className}
            data-active={activeView}
            onClick={() => onClick("chat")}
            variant={activeView === "chat" ? "default" : "secondary"}
          >
            <span>
              <MessageSquareIcon color="#FFFFFF" size="1.25rem" />
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Chat</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="Go to queue view"
            className={className}
            data-active={activeView}
            onClick={() => onClick("queue")}
            variant={activeView === "queue" ? "default" : "secondary"}
          >
            <span>
              <ListOrderedIcon color="#FFFFFF" size="1.25rem" />
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Video Queue</p>
        </TooltipContent>
      </Tooltip>

      <Tooltip>
        <TooltipTrigger asChild>
          <Button
            aria-label="Go to settings view"
            className={className}
            data-active={activeView}
            onClick={() => onClick("settings")}
            variant={activeView === "settings" ? "default" : "secondary"}
          >
            <span>
              <SettingsIcon color="#FFFFFF" size="1.25rem" />
            </span>
          </Button>
        </TooltipTrigger>
        <TooltipContent>
          <p>Settings</p>
        </TooltipContent>
      </Tooltip>
    </>
  );
};

export default RoomToolbar;

================
File: src/components/VideoRoom/Settings.tsx
================
import React, { lazy, useState } from "react";
import { ClipboardCopyIcon } from "lucide-react";

import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { CHANGE_SETTINGS } from "@/constants/socketActions";

import { useToast } from "@/components/ui/use-toast";
import { useSocket } from "@/context/SocketContext";

import { useCopyToClipboard } from "@/hooks/useCopyToClipboard";
import { runIfAuthorized } from "@/libs/utils/socket";
import { generateUserIcon } from "@/libs/utils/chat";
import { deepEqual } from "@/libs/utils/frontend-utils";
import useAudio from "@/hooks/useAudio";
import { BUTTON_PRESS_AUDIO } from "@/constants/constants";

const UserModal = lazy(() =>
  import("../Modals/UserModal").then((module) => {
    return { default: module.UserModal };
  }),
);

interface SettingsProps {}

const Settings: React.FC<SettingsProps> = () => {
  const { toast } = useToast();
  const { socket, room } = useSocket();

  const [isUserModalOpen, setIsUserModalOpen] = useState<string | null>(null);
  const [privateRoom, setPrivateRoom] = useState<boolean>(!!room?.private);
  const [maxRoomSize, setMaxRoomSize] = useState<number>(
    room?.maxRoomSize ?? 10,
  );
  const [roomPasscode, setRoomPasscode] = useState<string>(
    room?.passcode ?? "",
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  const [_value, copy] = useCopyToClipboard();

  const { play: playButtonClickSound } = useAudio({
    volume: 0.5,
    src: BUTTON_PRESS_AUDIO,
  });

  const isAuthorized =
    socket?.data.isAdmin || room?.host === socket?.data.userId;

  const handleChangePrivateRoom = (checked: boolean) => {
    playButtonClickSound();
    setPrivateRoom(!checked);
  };

  const handleChangeMaxRoomSize = (e: React.ChangeEvent<HTMLInputElement>) => {
    setMaxRoomSize(Number(e.target.value));
  };

  const handleChangeRoomPasscode = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRoomPasscode(e.target.value);
  };

  const handleCopyPasscode = () => {
    if (!room?.passcode) return;
    room?.passcode &&
      copy(room.passcode).then(() => {
        toast({
          variant: "info",
          title: "Passcode successfully copied!",
          description: <span>Share it with someone you trust 🤫</span>,
        });
      });
  };

  const handleSaveSettings = () => {
    if (socket?.data.isAdmin && room) {
      runIfAuthorized(
        room.host,
        socket.data.userId,
        () => {
          errorMessage && setErrorMessage(null);

          const newMaxRoomSize =
            room?.maxRoomSize === maxRoomSize ? undefined : maxRoomSize;

          const currentSettings = {
            private: room.private,
            maxRoomSize: room.maxRoomSize,
            roomPasscode: room.passcode,
          };

          const newSettings = {
            private: privateRoom,
            maxRoomSize,
            roomPasscode: roomPasscode.trim() || null,
          };

          const hasSettingsChanged = !deepEqual(currentSettings, newSettings);

          if (!hasSettingsChanged) return;

          if (newMaxRoomSize && newMaxRoomSize > 20) {
            setErrorMessage(
              "Max room size cannot be larger than 20 characters",
            );
            return;
          }

          socket.emit(CHANGE_SETTINGS, newSettings);

          toast({
            variant: "success",
            title: "Success!",
            description: "Room settings have successfully been saved!",
          });
        },
        socket.data.isAdmin,
      );
    }
  };

  const handleCloseUserModal = (userId: string | null) => {
    setIsUserModalOpen(userId);
  };

  return (
    <div className="flex flex-col flex-grow w-full h-full p-2 gap-4 hide-scrollbar">
      <div>
        {/* text-white p-2 rounded uppercase text-sm text-center ring-2 ring-[#6b2ed7] ring-inset */}
        {/* text-white p-2 rounded uppercase text-sm text-center bg-[#6b2ed7] ring-2 ring-[#6b2ed7] ring-inset */}
        <div className="">
          <div className="text-white font-semibold font-sans p-2 rounded uppercase text-sm text-center bg-[#6b2ed7] shadow-[inset_0_2px_3px_rgba(255,_255,_255,_0.3),_inset_0_-2px_3px_#4d219b,_0_1px_1px_#331567]">
            CONNECTED USERS: {room?.members.length}
          </div>
          <div className="mt-2 flex flex-col gap-2">
            {room &&
              Array.isArray(room?.members) &&
              room.members.map((member, index) => (
                <React.Fragment key={`user-modal-button-${member.id}`}>
                  <UserModal
                    key={`user-modal-button-${member.id}-${index}`}
                    userId={member.id}
                    buttonText={
                      <div>
                        <span className="mr-1">
                          {generateUserIcon(
                            member.id,
                            room.host,
                            member?.isAdmin,
                          )}
                        </span>
                        {member.username}
                      </div>
                    }
                    headerText={
                      <div>
                        User:{" "}
                        <span className="text-primary">{member.username}</span>
                      </div>
                    }
                    disabled={
                      member.isAdmin ||
                      !isAuthorized ||
                      member.id === socket?.data?.userId
                    }
                    open={isUserModalOpen === member.id}
                    handleToggle={handleCloseUserModal}
                  />
                </React.Fragment>
              ))}
          </div>
        </div>
      </div>
      <div className="flex items-center gap-x-2 text-sm text-secondary-foreground">
        <Switch
          id="public-mode"
          checked={!privateRoom}
          onCheckedChange={handleChangePrivateRoom}
        />
        <Label htmlFor="public-mode">Public</Label>
      </div>
      <div className="text-sm text-secondary-foreground">
        <Label htmlFor="max-room-size">Max Room Size</Label>
        <Input
          disabled={!isAuthorized}
          id="max-room-size"
          type="number"
          max={10}
          min={room?.members.length}
          value={maxRoomSize}
          onChange={handleChangeMaxRoomSize}
        />
        {!!maxRoomSize && room?.maxRoomSize !== maxRoomSize && (
          <span className="mt-0.5 block text-red-300">Unsaved</span>
        )}
      </div>
      <div className="text-sm text-secondary-foreground">
        <Label htmlFor="room-passcode">Room Passcode</Label>
        <div className="flex items-center">
          <Input
            className=""
            disabled={!isAuthorized}
            type="password"
            id="room-passcode"
            value={roomPasscode}
            onChange={handleChangeRoomPasscode}
          />
          <Button
            aria-label="Copy to clipboard"
            disabled={!roomPasscode.trim()}
            onClick={handleCopyPasscode}
            className="w-12 rounded-r rounded-l-none"
          >
            <span>
              <ClipboardCopyIcon color="#FFFFFF" size="1.25rem" />
            </span>
          </Button>
        </div>
        {!!roomPasscode && room?.passcode !== roomPasscode && (
          <span className="mt-0.5 block text-red-300">Unsaved</span>
        )}
      </div>

      <Button
        disabled={!isAuthorized}
        onClick={handleSaveSettings}
        className="w-full rounded"
      >
        Save
      </Button>
      {errorMessage && <span className="text-red-600">{errorMessage}</span>}
    </div>
  );
};

export default Settings;

================
File: src/components/CraftedWithLove.tsx
================
export const CraftedWithLove = () => {
  return (
    <div className="text-white text-xs text-center mt-5">
      Crafted with love by{" "}
      <a
        href="https://github.com/Xurify"
        target="_blank"
        rel="noopener noreferrer"
        className="hover:text-red-500"
      >
        Xurify
      </a>{" "}
      <span className="text-red-500">❤</span>
    </div>
  );
};

================
File: src/components/CreateRoomBox.tsx
================
import React, { useState } from "react";

import { useRouter } from "next/navigation";
import Link from "next/link";

import { useSocket } from "@/context/SocketContext";
import { useToast } from "@/components/ui/use-toast";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { DiceButton } from "@/components/DiceButton";
import { JoinRoomBoxProps } from "@/components/JoinRoomBox";

import { CREATE_ROOM } from "@/constants/socketActions";
import { CraftedWithLove } from "./CraftedWithLove";

export const CreateRoomBox: React.FC<JoinRoomBoxProps> = ({
  toggle: toggleShowJoin,
}) => {
  const [roomName, setRoomName] = useState("");
  const [username, setUsername] = useState("");

  const router = useRouter();
  const { toast } = useToast();
  const { socket } = useSocket();

  const handleChangeUsername = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUsername(value);
  };

  const handleChangeRoomName = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRoomName(value);
  };

  const handleGenerateRandomUsername = () => {
    import("@/libs/utils/names").then((module) => {
      setUsername(module.generateName());
    });
  };

  const handleGenerateRandomRoomName = () => {
    import("@/libs/utils/names").then((module) => {
      setRoomName(`${module.generateName().split(" ")[0]} Room`);
    });
  };

  const handleCreateRoom = () => {
    if (!roomName.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Room name is missing.",
      });
      return;
    } else if (!username.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Username is missing.",
      });
      return;
    } else if (socket && !socket?.connected) {
      console.log("Socket is not connected", socket);
    }

    socket?.emit(CREATE_ROOM, username, roomName, ({ result, error }) => {
      if (result && result.id) {
        router.push(`/room/${result.id}`);
      } else {
        toast({
          variant: "destructive",
          title: "Uh oh! Something went wrong",
          description: error,
        });
      }
    });
  };

  return (
    <div className="max-w-[30rem] w-full bg-card p-4 rounded">
      <div className="flex mb-3">
        <Input
          placeholder="Room name"
          onChange={handleChangeRoomName}
          value={roomName}
        />
        <DiceButton className="ml-2" onClick={handleGenerateRandomRoomName} />
      </div>
      <div className="flex">
        <Input
          placeholder="Username"
          onChange={handleChangeUsername}
          value={username}
        />
        <DiceButton className="ml-2" onClick={handleGenerateRandomUsername} />
      </div>
      <div className="mt-4 flex flex-col items-center justify-end">
        <Button
          className="w-full h-9 py-1 px-2 border uppercase"
          onClick={handleCreateRoom}
        >
          Create Room
        </Button>
        <Button
          className="w-full h-9 py-1 px-2 border uppercase mt-2"
          onClick={toggleShowJoin}
          variant="secondary"
        >
          Join Room
        </Button>
        <div className="flex items-center justify-center my-4 max-w-[10rem] w-full">
          <div className="flex-grow border-b border-gray-300"></div>
          <span className="px-4 text-gray-300 text-sm">or</span>
          <div className="flex-grow border-b border-gray-300"></div>
        </div>
        <Link
          className="inline-flex items-center justify-center rounded-md text-xs font-medium transition-colors bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 w-full h-9 py-1 px-2 border uppercase"
          href="/rooms"
        >
          Check out the Room Browser!
        </Link>
      </div>
      <CraftedWithLove />
    </div>
  );
};

export default CreateRoomBox;

================
File: src/components/DiceButton.tsx
================
import React from "react";
import { DicesIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/libs/utils/frontend-utils";
import useAudio from "@/hooks/useAudio";
import { BUTTON_PRESS_AUDIO } from "@/constants/constants";

export interface DiceButtonProps {
  className?: string;
  onClick: () => void;
}

export const DiceButton: React.FC<DiceButtonProps> = ({
  className,
  onClick,
}) => {
  const { play: playButtonClickSound } = useAudio({
    volume: 0.5,
    src: BUTTON_PRESS_AUDIO,
  });

  const handleClick = () => {
    playButtonClickSound();
    onClick?.();
  };

  return (
    <Button
      aria-label="Generate Random Name"
      className={cn(
        "w-9 h-9 min-w-[2.25rem] text-primary hover:bg-primary hover:text-white ml-2",
        className,
      )}
      onClick={handleClick}
      variant="secondary"
    >
      <span>
        <DicesIcon size="1.4rem" />
      </span>
    </Button>
  );
};

export default DiceButton;

================
File: src/components/ErrorBoundary.tsx
================
import React from "react";

export class ErrorBoundary extends React.Component<{ children: React.ReactElement }, { hasError: boolean }> {
  constructor(props: { children: React.ReactElement }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError() {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: any) {
    // You can also log the error to an error reporting service
    console.log("ERRORBOUNDARY:", error, errorInfo);
    //logErrorToMyService(error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      // You can render any custom fallback UI
      return <h1>Something went wrong.</h1>;
    }

    return this.props.children;
  }
}

================
File: src/components/EyeButton.tsx
================
import React from "react";
import { EyeIcon, EyeOffIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/libs/utils/frontend-utils";

export interface EyeButtonProps {
  active?: boolean;
  className?: string;
  onClick: () => void;
}

export const EyeButton: React.FC<EyeButtonProps> = ({ className, onClick, active }) => {
  const handleClick = () => {
    //playButtonClickSound();
    onClick && onClick();
  };

  return (
    <Button
      aria-label="Toggle show password"
      className={cn("w-9 h-9 min-w-[2.25rem] text-primary hover:bg-primary hover:text-white ml-2", {
        [`${className}`]: !!className,
      })}
      onClick={handleClick}
      variant="secondary"
    >
      <span>{active ? <EyeOffIcon size="1.4rem" /> : <EyeIcon size="1.4rem" />}</span>
    </Button>
  );
};

export default EyeButton;

================
File: src/components/JoinRoomBox.tsx
================
import React, { useState } from "react";

import { useRouter } from "next/navigation";
import Link from "next/link";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import DiceButton from "@/components/DiceButton";
import { useToast } from "@/components/ui/use-toast";

import { CHECK_IF_ROOM_EXISTS, JOIN_ROOM } from "@/constants/socketActions";
import { useSocket } from "@/context/SocketContext";
import { CraftedWithLove } from "./CraftedWithLove";

export interface JoinRoomBoxProps {
  toggle: () => void;
}

export const JoinRoomBox: React.FC<JoinRoomBoxProps> = ({
  toggle: toggleShowCreate,
}) => {
  const [roomId, setRoomId] = useState("");
  const [username, setUsername] = useState("");

  const router = useRouter();
  const { socket } = useSocket();
  const { toast } = useToast();

  const handleChangeUsername = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUsername(value);
  };

  const handleChangeRoomId = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setRoomId(value);
  };

  const handleGenerateRandomUsername = () => {
    import("@/libs/utils/names").then((module) => {
      setUsername(module.generateName());
    });
  };

  const handleJoinRoom = () => {
    if (!roomId.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Room Id is missing.",
      });
      return;
    } else if (!username.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Username is missing.",
      });
      return;
    }

    socket?.emit(CHECK_IF_ROOM_EXISTS, roomId, (value) => {
      if (value === null) {
        toast({
          variant: "destructive",
          title: "Uh oh! Something went wrong",
          description: "Sorry, this room doesn't exist. 😥",
        });
      } else {
        socket.emit(JOIN_ROOM, roomId, username, ({ success }) => {
          success && router.push(`/room/${roomId}`);
        });
      }
    });
  };

  return (
    <div className="max-w-[30rem] w-full bg-card p-4 rounded">
      <div className="flex">
        <Input
          placeholder="Room Id"
          onChange={handleChangeRoomId}
          value={roomId}
        />
      </div>
      <div className="flex mt-3">
        <Input
          placeholder="Username"
          onChange={handleChangeUsername}
          value={username}
        />
        <DiceButton className="ml-2" onClick={handleGenerateRandomUsername} />
      </div>
      <div className="mt-4 flex flex-col items-center justify-end">
        <Button
          className="w-full h-9 py-1 px-2 border uppercase"
          onClick={handleJoinRoom}
          variant="default"
        >
          Join Room
        </Button>
        <Button
          className="w-full h-9 py-1 px-2 border uppercase mt-2"
          onClick={toggleShowCreate}
          variant="secondary"
        >
          Create Room
        </Button>
        <div className="flex items-center justify-center my-4 max-w-[10rem] w-full">
          <div className="flex-grow border-b border-gray-300"></div>
          <span className="px-4 text-gray-300 text-sm">or</span>
          <div className="flex-grow border-b border-gray-300"></div>
        </div>
        <Link
          className="inline-flex items-center justify-center rounded-md text-xs font-medium transition-colors bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 w-full h-9 py-1 px-2 border uppercase"
          href="/rooms"
        >
          Check out the Room Browser!
        </Link>
      </div>
      <CraftedWithLove />
    </div>
  );
};

export default JoinRoomBox;

================
File: src/components/Page.tsx
================
import React from "react";
import posthog from "posthog-js";
import PageHead from "./PageHead";
import NavigationHeader, {
  NavigationHeaderProps,
} from "./Header/NavigationHeader";

interface PageProps {
  navigationHeaderProps: NavigationHeaderProps;
  sessionToken: string | null;
}

const postHogKey: string = process.env.NEXT_PUBLIC_POSTHOG_KEY || "";

export const Page: React.FC<React.PropsWithChildren<PageProps>> = ({
  children,
  navigationHeaderProps = {},
  sessionToken,
}) => {
  React.useEffect(() => {
    if (["www.synkro.live", "synkro.vercel.app"].includes(location.host)) {
      posthog.init(postHogKey, {
        api_host: `https://${location.host}/ingest`,
        ui_host: "https://us.posthog.com",
        loaded: function (posthog) {
          sessionToken && posthog.identify(sessionToken);
        },
        person_profiles: "always",
        enable_recording_console_log: true,
        secure_cookie: true,
        session_recording: {
          maskAllInputs: false,
          maskInputOptions: {
            password: true,
          },
        },
      });
    }
  }, []);

  return (
    <>
      <PageHead />
      <NavigationHeader {...navigationHeaderProps} />
      <div className="md:px-2 h-full md:h-[calc(100vh-64px)] relative md:pb-4">
        {children}
      </div>
    </>
  );
};

================
File: src/components/PageHead.tsx
================
import React from "react";
import Head from "next/head";

export const PageHead: React.FC = () => {
  return (
    <Head>
      <title>Synkro - Watch Together</title>
      <meta
        name="description"
        content="Synkro - The app made for watching videos with friends!"
        key="desc"
      />
      <meta property="og:image" content="/api/og" />
      <meta property="og:title" content="Synkro" />
      <meta property="og:description" content="Synkro - Watch Together" />
      <meta property="og:url" content="https://www.synkro.live" />
      <meta property="twitter:image" content="/api/og" />
      <meta property="twitter:card" content="app" />
      <meta property="twitter:title" content="Synkro" />
      <meta property="twitter:description" content="Synkro - Watch Together" />
      <meta name="viewport" content="width=device-width, initial-scale=1" />
    </Head>
  );
};

export default PageHead;

================
File: src/components/Separator.tsx
================
export const Separator = ({ className = "" }: { className?: string }) => (
  <div
    className={`separator bg-secondary w-px min-w-[1px] my-0 mx-[10px] ${className}`.trim()}
    data-orientation="vertical"
    aria-orientation="vertical"
    role="separator"
  />
);

================
File: src/components/Sidebar.tsx
================
import { SidebarViews } from "./VideoRoom/RoomToolbar";

interface SidebarProps {
  activeView: SidebarViews;
  views: {
    [key in SidebarViews]: React.ReactNode;
  };
}

export const Sidebar: React.FC<SidebarProps> = ({ activeView, views }) => {
  return (
    <div className="bg-white dark:bg-[#0000009c] md:dark:bg-[#0000006e] mt-2 md:mt-0 md:ml-2 shadow-md rounded overflow-y-hidden md:w-[18rem] min-w-[18rem] w-full h-full md:h-auto flex">
      <div className="flex flex-col flex-1 md:min-h-[calc(100vh-158px)] w-full">
        {views[activeView]}
      </div>
    </div>
  );
};

export default Sidebar;

================
File: src/components/Spinner.tsx
================
export const Spinner = () => {
  return (
    <div role="status">
      <svg
        aria-hidden="true"
        className="w-10 h-10 text-black animate-spin fill-primary"
        viewBox="0 0 100 101"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z"
          fill="currentColor"
        />
        <path
          d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z"
          fill="currentFill"
        />
      </svg>
      <span className="sr-only">Loading...</span>
    </div>
  );
};

================
File: src/components/StarField.tsx
================
import React, { Component, RefObject } from "react";

interface IStar {
  X: number;
  Y: number;
  SX: number;
  SY: number;
  W: number;
  H: number;
  age: number;
  dies: number;
  C: string;
}

interface IStarFieldState {
  stars: IStar[];
  acceleration: number;
  starsToDraw: number;
}

class StarField extends Component<{}, IStarFieldState> {
  private fieldRef: RefObject<HTMLCanvasElement>;
  private interval?: number;

  constructor(props: {}) {
    super(props);

    this.state = {
      stars: [],
      acceleration: 1,
      starsToDraw: 0,
    };

    this.fieldRef = React.createRef() as React.RefObject<HTMLCanvasElement>;
  }

  getUrlParameter = (sParam: string): string | boolean | undefined => {
    const sPageURL = decodeURIComponent(window.location.search.substring(1));
    const sURLVariables = sPageURL.split("&");
    let sParameterName;

    for (let i = 0; i < sURLVariables.length; i++) {
      sParameterName = sURLVariables[i].split("=");
      if (sParameterName[0] === sParam) {
        return sParameterName[1] === undefined ? true : sParameterName[1];
      }
    }
  };

  componentDidMount() {
    const starsToDraw =
      (this.fieldRef.current!.width * this.fieldRef.current!.height) / 833.33;
    const acceleration = Number(this.getUrlParameter("accel")) || 1;

    this.setState({
      starsToDraw: Number(this.getUrlParameter("stars")) || starsToDraw,
      acceleration: acceleration,
    });

    this.fieldRef.current!.width = window.innerWidth;
    this.fieldRef.current!.height = window.innerHeight;

    this.interval = window.setInterval(this.draw, 40);
  }

  componentWillUnmount() {
    if (this.interval) {
      clearInterval(this.interval);
    }
  }

  createStar = (): IStar => {
    const field = this.fieldRef.current!;
    const star: IStar = {
      X: field.width / 2,
      Y: field.height / 2,
      SX: Math.random() * 5 - 2.5,
      SY: Math.random() * 5 - 2.5,
      W: 1,
      H: 1,
      age: 0,
      dies: 500,
      C: "#ffffff",
    };

    let start = field.width > field.height ? field.width : field.height;
    star.X += (star.SX * start) / 5;
    star.Y += (star.SY * start) / 5;

    return star;
  };

  drawStar = (star: IStar): IStar | null => {
    const fieldCanvasElement = this.fieldRef.current!;
    const fieldContext = fieldCanvasElement.getContext("2d");

    star.X += star.SX;
    star.Y += star.SY;

    star.SX += star.SX / (50 / this.state.acceleration);
    star.SY += star.SY / (50 / this.state.acceleration);
    star.age++;

    if (
      [
        Math.floor(50 / this.state.acceleration),
        Math.floor(150 / this.state.acceleration),
        Math.floor(300 / this.state.acceleration),
      ].includes(star.age)
    ) {
      star.W++;
      star.H++;
    }

    if (
      star.X + star.W < 0 ||
      star.X > fieldCanvasElement.width ||
      star.Y + star.H < 0 ||
      star.Y > fieldCanvasElement.height
    ) {
      return null;
    }

    if (fieldContext) {
      fieldContext.fillStyle = star.C;
      fieldContext.fillRect(star.X, star.Y, star.W, star.H);
    }
    return star;
  };

  draw = () => {
    const field = this.fieldRef.current!;
    const fieldContext = field.getContext("2d")!;

    if (field.width !== window.innerWidth) field.width = window.innerWidth;
    if (field.height !== window.innerHeight) field.height = window.innerHeight;

    fieldContext.fillStyle = "rgba(0, 0, 0, 0.8)";
    fieldContext.fillRect(0, 0, field.width, field.height);

    const updatedStars = this.state.stars
      .map(this.drawStar)
      .filter((star) => star !== null) as IStar[];

    while (updatedStars.length < this.state.starsToDraw) {
      updatedStars.push(this.createStar());
    }

    this.setState({ stars: updatedStars });
  };

  render() {
    return (
      <div className="star-field-canvas-wrapper absolute w-full">
        <canvas
          className="max-h-[calc(100vh-90px)] w-full"
          ref={this.fieldRef}
          id="field"
        ></canvas>
      </div>
    );
  }
}

export default StarField;

================
File: src/constants/constants.ts
================
export const PRODUCTION_SITE_URL = "https://www.synkro.live";
export const BASE_URL =
  process.env.NODE_ENV === "production"
    ? PRODUCTION_SITE_URL
    : "http://localhost:3000";

export const SOCKET_URL: string =
  process.env.NODE_ENV === "development"
    ? "ws://localhost:8000"
    : (process.env.NEXT_PUBLIC_SERVER_API as string);

export const SERVER_URL: string =
  process.env.NODE_ENV === "development"
    ? "http://localhost:8000"
    : `https://${process.env.NEXT_PUBLIC_SERVER_BASE_API}`;

export const YOUTUBE_VIDEO_URL_REGEX =
  /(youtu.*be.*)\/(watch\?v=|embed\/|v|shorts|)(.*?((?=[&#?])|$))/;
export const SOUNDCLOUD_TRACK_URL_REGEX =
  /^https?:\/\/(soundcloud\.com|snd\.sc)\/(.*)$/;
export const SOUNDCLOUD_TRACK_SHORT_URL_REGEX =
  /^https?:\/\/(on.soundcloud\.com|snd\.sc)\/(.*)$/;
export const VIMEO_VIDEO_URL_REGEX = /^https?:\/\/(vimeo\.com)\/(.*)$/;
export const WISTIA_VIDEO_URL_REGEX =
  /https?:\/\/[^.]+\.(wistia\.net|wistia\.com|wi\.st)\/(medias|embed\/iframe)\/([a-zA-Z0-9]*)/;
export const DAILYMOTION_URL_REGEX =
  /https?:\/\/www\.dailymotion.com\/video\/([a-zA-Z0-9]*)/;
export const TWITCH_URL_REGEX = /https?:\/\/www\.twitch\.tv\/videos\/([0-9]*)/;

export const VIDEO_FILE_URL_REGEX =
  /((?:https?(?:%3A%2F%2F|:\/\/))(?:www\.)?(?:\S+)(?:%2F|\/)(?:(?!\.(?:mp4|mkv|wmv|m4v|mov|avi|flv|webm|flac|mka|m4a|aac|ogg))[^\/])*\.(mp4|mkv|wmv|m4v|mov|avi|flv|webm|flac|mka|m4a|aac|ogg))(?!\/|\.[a-z]{1,3})/;
export const AUDIO_FILE_URL_REGEX = /^https?:\/\/(.*)(\.mp3|.wav)/;
export const URL_REGEX =
  /https?:\/\/(www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()!@:%_\+.~#?&\/\/=]*)/;

//https:\/\/.*.mp4

export const BUTTON_PRESS_AUDIO =
  "https://utfs.io/f/0c5f10fd-9e8b-401a-94bb-032d7820bda4-lbqvhy.mp3";
export const USER_DISCONNECTED_AUDIO =
  "https://utfs.io/f/c77e24f4-9d3b-4a2d-98d9-6787d2280744-tmke27.mp3"; // Mixkit.co
export const USER_JOINED_AUDIO =
  "https://utfs.io/f/bc2ccdfa-a4d0-4b79-9b98-f41034efa2ab-6tmzer.mp3"; // Mixkit.co
export const USER_KICKED_AUDIO =
  "https://utfs.io/f/b3b7e3cc-3abe-4e97-a813-028a9f20bae6-ml9rpw.mp3"; // ElevenLabs

================
File: src/constants/socketActions.ts
================
export const JOIN_ROOM = "JOIN_ROOM";
export const JOIN_ROOM_BY_INVITE = "JOIN_ROOM_BY_INVITE";
export const LEAVE_ROOM = "LEAVE_ROOM";
export const RECONNECT_USER = "RECONNECT_USER";
export const GET_ROOM_ID = "GET_ROOM_ID";
export const CHANGE_ROOM_ID = "CHANGE_ROOM_ID";
export const GET_ROOM_INFO = "GET_ROOM_INFO";
export const GET_USER_INFO = "GET_USER_INFO";
export const GET_ROOM_CODE = "GET_ROOM_CODE";
export const SET_ROOM_PASSCODE = "SET_ROOM_PASSCODE";
export const VERIFY_ROOM_PASSCODE = "VERIFY_ROOM_PASSCODE";
export const SET_ROOM_MAX_SIZE = "SET_ROOM_MAX_SIZE";
export const CHECK_IF_ROOM_IS_FULL = "CHECK_IF_ROOM_IS_FULL";
export const CHECK_IF_ROOM_EXISTS = "CHECK_IF_ROOM_EXISTS";
export const CREATE_ROOM = "CREATE_ROOM";
export const CHECK_IF_ROOM_REQUIRES_PASSCODE = "CHECK_IF_ROOM_REQUIRES_PASSCODE";
export const PLAY_VIDEO = "PLAY_VIDEO";
export const PAUSE_VIDEO = "PAUSE_VIDEO";
export const REWIND_VIDEO = "REWIND_VIDEO";
export const FASTFORWARD_VIDEO = "FASTFORWARD_VIDEO";
export const BUFFERING_VIDEO = "BUFFERING_VIDEO";
export const END_OF_VIDEO = "END_OF_VIDEO";
export const CHANGE_VIDEO = "CHANGE_VIDEO";
export const SYNC_TIME = "SYNC_TIME";
export const SYNC_VIDEO_INFORMATION = "SYNC_VIDEO_INFORMATION";
export const GET_VIDEO_INFORMATION = "GET_VIDEO_INFORMATION";
export const GET_HOST_VIDEO_INFORMATION = "GET_HOST_VIDEO_INFORMATION";
export const GET_HOST_TIME = "GET_HOST_TIME";
export const SYNC_WITH_HOST = "SYNC_WITH_HOST";
export const SYNC_BUTTON_PRESSED = "SYNC_BUTTON_PRESSED";
export const USER_MESSAGE = "USER_MESSAGE";
export const SERVER_MESSAGE = "SERVER_MESSAGE";
export const GET_USERS = "GET_USERS";
export const SET_HOST = "SET_HOST";
export const KICK_USER = "KICK_USER";
export const GET_VIDEO_QUEUE = "GET_VIDEO_QUEUE";
export const ADD_VIDEO_TO_QUEUE = "ADD_VIDEO_TO_QUEUE";
export const REMOVE_VIDEO_FROM_QUEUE = "REMOVE_VIDEO_FROM_QUEUE";
export const VIDEO_QUEUE_REORDERED = "VIDEO_QUEUE_REORDERED";
export const VIDEO_QUEUE_CLEARED = "VIDEO_QUEUE_CLEARED";
export const CHANGE_SETTINGS = "CHANGE_SETTINGS";
export const SET_ADMIN = "SET_ADMIN";
export const USER_VIDEO_STATUS = "USER_VIDEO_STATUS";

================
File: src/context/SocketContext.tsx
================
import { Room, User } from "@/types/interfaces";
import { CustomSocket } from "@/types/socketCustomTypes";
import React from "react";

export type SocketContextType = {
  socket: CustomSocket | null;
  sessionToken: string | null;
  room: Room | null | undefined;
  user: User | null;
  isConnecting: boolean;
};

export const SocketContext = React.createContext<SocketContextType>({
  socket: null,
  sessionToken: null,
  room: null,
  user: null,
  isConnecting: true,
});

export const useSocket = () => React.useContext(SocketContext);

================
File: src/context/SocketProvider.tsx
================
import React from "react";
import io from "socket.io-client";
import { useRouter } from "next/router";
import { SocketContext } from "./SocketContext";
import { CustomSocket } from "@/types/socketCustomTypes";
import { SOCKET_URL } from "@/constants/constants";
import { GET_ROOM_INFO, GET_USER_INFO, SET_ADMIN } from "@/constants/socketActions";
import { Room, User } from "@/types/interfaces";

interface SocketProviderProps {
  sessionToken: string | null;
  adminToken?: string;
}

export const SocketProvider: React.FC<React.PropsWithChildren<SocketProviderProps>> = ({
  children,
  sessionToken,
  adminToken,
}) => {
  const [socket, setSocket] = React.useState<CustomSocket | null>(null);
  const [room, setRoom] = React.useState<Room | null | undefined>(undefined);
  const [user, setUser] = React.useState<User | null>(null);
  const [isConnecting, setIsConnecting] = React.useState<boolean>(true);
  const [retries, setRetries] = React.useState(3);

  const router = useRouter();

  React.useEffect(() => {
    if (!sessionToken && router.pathname !== "/404") {
      if (retries > 0) {
        setRetries((prevRetries) => prevRetries - 1);
        router.replace(router.asPath);
      }
      return;
    }
    const newSocket = io(SOCKET_URL, {
      transports: ["websocket"],
      auth: {
        token: sessionToken,
        adminToken,
      },
    }) as unknown as CustomSocket;

    newSocket.data = {
      userId: '',
      roomId: '',
      isAdmin: false,
    };

    if (sessionToken) {
      newSocket.data.userId = sessionToken;
      newSocket.data.roomId = room?.id || '';
      newSocket.data.isAdmin = room?.host === newSocket.data.userId;
    }

    setSocket(newSocket);

    newSocket.on("connect_error", (err) => {
      console.log(`connect_error due to ${err.message}`);
    });

    newSocket.on("connect", () => {
      console.log("Connected");
      setIsConnecting(false);
    });

    newSocket.on("disconnect", () => {
      setIsConnecting(false);
      setRoom(null);
    });

    // TODO: Test setting admin and changing to new admins
    newSocket.on(SET_ADMIN, () => {
      newSocket.data.isAdmin = true;
    });

    newSocket.on(GET_ROOM_INFO, (newRoom) => {
      setRoom(newRoom);
      newSocket.data.roomId = newRoom?.id || '';
      newSocket.data.isAdmin = newRoom?.host === newSocket.data.userId;
    });

    newSocket.on(GET_USER_INFO, (newUser) => {
      setUser(newUser);
    });

    return () => {
      newSocket.disconnect();
    };
  }, [sessionToken]);

  return (
    <SocketContext.Provider value={{ socket, sessionToken, room, user, isConnecting }}>
      {children}
    </SocketContext.Provider>
  );
};

export default SocketContext;

================
File: src/hooks/useAudio.ts
================
import { useEffect, useState, useRef } from "react";

const audioCache: { [key: string]: HTMLAudioElement } = {};

interface AudioOptions {
  volume?: number;
  src: string;
}

function useAudio({ volume = 1.0, src = "" }: AudioOptions) {
  const [isPlaying, setIsPlaying] = useState(false);
  const audioRef = useRef<HTMLAudioElement | null>(null);

  useEffect(() => {
    const audioUrl = encodeURIComponent(src);
    const proxyUrl = `/api/audio-proxy?url=${audioUrl}`;

    if (src && !audioCache[src]) {
      const audio = new Audio();
      audio.volume = volume;
      audio.preload = "auto";
      audio.src = proxyUrl;
      audioCache[src] = audio;
    }

    if (src) {
      audioRef.current = audioCache[src];
      audioRef.current.volume = volume;
    }

    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
      }
    };
  }, [src, volume]);

  const play = () => {
    if (audioRef.current) {
      if (audioRef.current.currentTime > 0) {
        audioRef.current.currentTime = 0;
      }

      audioRef.current.play().then(() => {
        setIsPlaying(true);
      });
    }
  };

  const pause = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      setIsPlaying(false);
    }
  };

  const togglePlayPause = () => {
    if (isPlaying) {
      pause();
    } else {
      play();
    }
  };

  const setVolume = (newVolume: number) => {
    if (newVolume >= 0 && newVolume <= 1) {
      if (audioRef.current) {
        audioRef.current.volume = newVolume;
      }
    } else {
      console.error("Volume must be between 0 and 1.");
    }
  };

  const seek = (time: number) => {
    if (audioRef.current) {
      audioRef.current.currentTime = time;
    }
  };

  const enableLooping = (loop: boolean) => {
    if (audioRef.current) {
      audioRef.current.loop = loop;
    }
  };

  const stop = () => {
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
      setIsPlaying(false);
    }
  };

  return {
    play,
    pause,
    togglePlayPause,
    setVolume,
    seek,
    enableLooping,
    stop,
    isPlaying,
  };
}

export default useAudio;

================
File: src/hooks/useCopyToClipboard.ts
================
import { useState } from "react";

type CopiedValue = string | null;
type CopyFn = (text: string) => Promise<boolean>;

export function useCopyToClipboard(): [CopiedValue, CopyFn] {
  const [copiedText, setCopiedText] = useState<CopiedValue>(null);

  const copy: CopyFn = async (text) => {
    if (!navigator?.clipboard) {
      console.warn("Clipboard not supported");
      return false;
    }

    try {
      await navigator.clipboard.writeText(text);
      setCopiedText(text);
      return true;
    } catch (error) {
      console.warn("Copy failed", error);
      setCopiedText(null);
      return false;
    }
  };

  return [copiedText, copy];
}

================
File: src/hooks/useDarkMode.ts
================
import { useEffect } from "react";
import { useLocalStorage } from "./useLocalStorage";
import { useUpdateEffect } from "./useUpdateEffect";
import { useMediaQuery } from "./useMediaQuery";

const COLOR_SCHEME_QUERY = "(prefers-color-scheme: dark)";

interface UseDarkModeOutput {
  isDarkMode: boolean;
  toggle: () => void;
  enable: () => void;
  disable: () => void;
}

export function useDarkMode(defaultValue?: boolean): UseDarkModeOutput {
  const isDarkOS = useMediaQuery(COLOR_SCHEME_QUERY);
  const [isDarkMode, setDarkMode] = useLocalStorage<boolean>("dark-mode", defaultValue ?? isDarkOS ?? false);

  useEffect(() => {
    document.body.className = isDarkMode ? "dark" : "light";
  }, [isDarkMode]);

  useUpdateEffect(() => {
    setDarkMode(isDarkOS);
  }, [isDarkOS]);

  return {
    isDarkMode,
    toggle: () => setDarkMode((prev) => !prev),
    enable: () => setDarkMode(true),
    disable: () => setDarkMode(false),
  };
}

================
File: src/hooks/useEventCallback.ts
================
import { useCallback, useRef } from "react";

import { useIsomorphicLayoutEffect } from "./useIsomorphicLayoutEffect";

export function useEventCallback<Args extends unknown[], R>(fn: (...args: Args) => R) {
  const ref = useRef<typeof fn>(() => {
    throw new Error("Cannot call an event handler while rendering.");
  });

  useIsomorphicLayoutEffect(() => {
    ref.current = fn;
  }, [fn]);

  return useCallback((...args: Args) => ref.current(...args), [ref]);
}

================
File: src/hooks/useEventListener.ts
================
import { RefObject, useEffect, useRef } from "react";
import { useIsomorphicLayoutEffect } from "./useIsomorphicLayoutEffect";

function useEventListener<K extends keyof MediaQueryListEventMap>(
  eventName: K,
  handler: (event: MediaQueryListEventMap[K]) => void,
  element: RefObject<MediaQueryList>,
  options?: boolean | AddEventListenerOptions
): void;

function useEventListener<K extends keyof WindowEventMap>(
  eventName: K,
  handler: (event: WindowEventMap[K]) => void,
  element?: undefined,
  options?: boolean | AddEventListenerOptions
): void;

function useEventListener<K extends keyof HTMLElementEventMap, T extends HTMLElement = HTMLDivElement>(
  eventName: K,
  handler: (event: HTMLElementEventMap[K]) => void,
  element: RefObject<T>,
  options?: boolean | AddEventListenerOptions
): void;

function useEventListener<K extends keyof DocumentEventMap>(
  eventName: K,
  handler: (event: DocumentEventMap[K]) => void,
  element: RefObject<Document>,
  options?: boolean | AddEventListenerOptions
): void;

function useEventListener<
  KW extends keyof WindowEventMap,
  KH extends keyof HTMLElementEventMap,
  KM extends keyof MediaQueryListEventMap,
  T extends HTMLElement | MediaQueryList | void = void
>(
  eventName: KW | KH | KM,
  handler: (event: WindowEventMap[KW] | HTMLElementEventMap[KH] | MediaQueryListEventMap[KM] | Event) => void,
  element?: RefObject<T>,
  options?: boolean | AddEventListenerOptions
) {
  const savedHandler = useRef(handler);

  useIsomorphicLayoutEffect(() => {
    savedHandler.current = handler;
  }, [handler]);

  useEffect(() => {
    const targetElement: T | Window = element?.current ?? window;

    if (!(targetElement && targetElement.addEventListener)) return;

    const listener: typeof handler = (event) => savedHandler.current(event);

    targetElement.addEventListener(eventName, listener, options);

    return () => {
      targetElement.removeEventListener(eventName, listener, options);
    };
  }, [eventName, element, options]);
}

export { useEventListener };

================
File: src/hooks/useIsFirstRender.ts
================
import { useRef } from "react";

export function useIsFirstRender(): boolean {
  const isFirst = useRef(true);

  if (isFirst.current) {
    isFirst.current = false;

    return true;
  }

  return isFirst.current;
}

================
File: src/hooks/useIsomorphicLayoutEffect.ts
================
import { useEffect, useLayoutEffect } from "react";

export const useIsomorphicLayoutEffect = typeof window !== "undefined" ? useLayoutEffect : useEffect;

================
File: src/hooks/useLocalStorage.ts
================
import { Dispatch, SetStateAction, useCallback, useEffect, useState } from "react";

import { useEventListener } from "./useEventListener";
import { useEventCallback } from "./useEventCallback";

declare global {
  interface WindowEventMap {
    "local-storage": CustomEvent;
  }
}

type SetValue<T> = Dispatch<SetStateAction<T>>;

export function useLocalStorage<T>(key: string, initialValue: T): [T, SetValue<T>] {
  const readValue = useCallback((): T => {
    if (typeof window === "undefined") {
      return initialValue;
    }

    try {
      const item = window.localStorage.getItem(key);
      return item ? (parseJSON(item) as T) : initialValue;
    } catch (error) {
      console.warn(`Error reading localStorage key “${key}”:`, error);
      return initialValue;
    }
  }, [initialValue, key]);

  const [storedValue, setStoredValue] = useState<T>(readValue);

  const setValue: SetValue<T> = useEventCallback((value) => {
    if (typeof window === "undefined") {
      console.warn(`Tried setting localStorage key “${key}” even though environment is not a client`);
    }

    try {
      const newValue = value instanceof Function ? value(storedValue) : value;
      window.localStorage.setItem(key, JSON.stringify(newValue));
      setStoredValue(newValue);
      window.dispatchEvent(new Event("local-storage"));
    } catch (error) {
      console.warn(`Error setting localStorage key “${key}”:`, error);
    }
  });

  useEffect(() => {
    setStoredValue(readValue());
  }, []);

  const handleStorageChange = useCallback(
    (event: StorageEvent | CustomEvent) => {
      if ((event as StorageEvent)?.key && (event as StorageEvent).key !== key) {
        return;
      }
      setStoredValue(readValue());
    },
    [key, readValue]
  );

  useEventListener("storage", handleStorageChange);

  useEventListener("local-storage", handleStorageChange);

  return [storedValue, setValue];
}

const parseJSON = <T>(value: string | null): T | undefined => {
  try {
    return value === "undefined" ? undefined : JSON.parse(value ?? "");
  } catch {
    console.error("Parsing error on", { value });
    return undefined;
  }
};

================
File: src/hooks/useMediaQuery.ts
================
import { useEffect, useState } from "react";

export function useMediaQuery(query: string): boolean {
  const getMatches = (query: string): boolean => {
    if (typeof window !== "undefined") {
      return window.matchMedia(query).matches;
    }
    return false;
  };

  const [matches, setMatches] = useState<boolean>(getMatches(query));

  function handleChange() {
    setMatches(getMatches(query));
  }

  useEffect(() => {
    const matchMedia = window.matchMedia(query);

    handleChange();

    if (matchMedia.addListener) {
      matchMedia.addListener(handleChange);
    } else {
      matchMedia.addEventListener("change", handleChange);
    }

    return () => {
      if (matchMedia.removeListener) {
        matchMedia.removeListener(handleChange);
      } else {
        matchMedia.removeEventListener("change", handleChange);
      }
    };
  }, [query]);

  return matches;
}

================
File: src/hooks/useQueue.ts
================
import { useState } from "react";

export interface Queue<T> {
  first: T | null;
  last: T | null;
  size: number;
  queue: T[];
  add: (element: T) => void;
  remove: () => void;
  removeItem: <K extends keyof T>(key: K, value: T[K]) => void;
  set: (elements: T[]) => void;
  clear: () => void;
}

export const useQueue = <T>(): Queue<T> => {
  const [queue, setQueue] = useState<T[]>([]);

  const add = (element: T) => {
    setQueue((prevQueue) => [...prevQueue, element]);
  };

  const remove = () => {
    if (queue.length === 0) return;
    setQueue((prevQueue) => prevQueue.slice(1));
  };

  const removeItem = <K extends keyof T>(key: K, value: T[K]) => {
    setQueue((prevQueue) => prevQueue.filter((item) => item[key] !== value));
  };

  const set = (newQueue: T[]) => {
    setQueue(newQueue);
  };

  const clear = () => {
    setQueue([]);
  };

  const first = queue.length > 0 ? queue[0] : null;
  const last = queue.length > 0 ? queue[queue.length - 1] : null;
  const size = queue.length;

  return { first, last, size, queue, add, remove, removeItem, clear, set };
};

export default useQueue;

================
File: src/hooks/useSSE.ts
================
import { useEffect, useRef } from "react";

interface SSEOptions {
  onMessage: (event: MessageEvent) => void;
  onOpen: () => void;
  onError: (event: Event) => void;
  onReconnect: () => void;
  reconnectDelay?: number;
}

export const useSSE = (url: string, options: SSEOptions) => {
  let eventSource: EventSource | null = null;
  const reconnectTimeoutRef = useRef<number | null>(null);

  useEffect(() => {
    const { onMessage, onOpen, onError, onReconnect } = options;

    const connect = () => {
      eventSource = new EventSource(url);

      eventSource.onmessage = (event) => {
        onMessage && onMessage(event);
      };

      eventSource.onopen = () => {
        onOpen && onOpen();
      };

      eventSource.onerror = (event) => {
        if (eventSource && eventSource.readyState === EventSource.CLOSED) {
          if (!document.hidden) {
            onReconnect && onReconnect();
            reconnect();
          }
        } else {
          onError && onError(event);
        }
      };
    };

    connect();

    const reconnect = () => {
      reconnectTimeoutRef.current = window.setTimeout(() => {
        connect();
      }, options?.reconnectDelay ?? 1000);
    };

    return () => {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }

      if (reconnectTimeoutRef.current !== null) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    };
  }, [url, options]);

  return {
    close: () => {
      if (eventSource) {
        eventSource.close();
        eventSource = null;
      }

      if (reconnectTimeoutRef.current !== null) {
        clearTimeout(reconnectTimeoutRef.current);
        reconnectTimeoutRef.current = null;
      }
    },
  };
};

export default useSSE;

================
File: src/hooks/useUpdateEffect.ts
================
import { DependencyList, EffectCallback, useEffect } from "react";

import { useIsFirstRender } from "./useIsFirstRender";

export function useUpdateEffect(effect: EffectCallback, deps?: DependencyList) {
  const isFirst = useIsFirstRender();

  useEffect(() => {
    if (!isFirst) {
      return effect();
    }
  }, deps);
}

================
File: src/libs/utils/chat.ts
================
import { ServerMessageType } from "@/types/interfaces";

export const isServerMessage = (messageType: ServerMessageType | "USER"): boolean => {
  return Object.values(ServerMessageType).includes(messageType as ServerMessageType);
};

export const isClientMessage = (messageType: string): boolean => {
  return messageType === "USER";
};

export const getMessageClassname = (type: ServerMessageType | "USER" | "ADMIN"): string | undefined => {
  const defaultMessageClassname = "bg-[#171923] border border-gray-600 text-primary-foreground";

  switch (type) {
    case "USER":
      return `user-message ${defaultMessageClassname}`;
    case "ADMIN":
      //`user-message border border-[#b54eff] bg-[#431961] text-primary-foreground text-[#ffec87]`;
      return "user-message admin border border-[#b54eff] bg-[#431961] text-primary-foreground";
    case ServerMessageType.ALERT:
      return "border border-orange-600 bg-[#67340f] text-primary-foreground";
    case ServerMessageType.USER_JOINED:
      return "border border-blue-400 bg-[#224655] text-primary-foreground";
    case ServerMessageType.USER_RECONNECTED:
      return "border border-blue-500 bg-[#2b3b5d] text-primary-foreground";
    case ServerMessageType.USER_DISCONNECTED:
      return "border border-destructive bg-[#471b1b] text-primary-foreground";
    case ServerMessageType.NEW_HOST:
      return "border border-[#eff50a] bg-[#f8e33f] text-black";
    case ServerMessageType.ERROR:
      return "bg-red-500 text-primary-foreground";
    default:
      return `${defaultMessageClassname}`;
  }
};

export const generateUserIcon = (member: string, host: string, isAdmin?: boolean): "👑" | "🔑" | null => {
  if (isAdmin) return "🔑";
  if (!member || !host) return null;
  return member === host ? "👑" : null;
};

================
File: src/libs/utils/frontend-utils.ts
================
import {
  AUDIO_FILE_URL_REGEX,
  DAILYMOTION_URL_REGEX,
  SOUNDCLOUD_TRACK_SHORT_URL_REGEX,
  SOUNDCLOUD_TRACK_URL_REGEX,
  TWITCH_URL_REGEX,
  URL_REGEX,
  VIDEO_FILE_URL_REGEX,
  VIMEO_VIDEO_URL_REGEX,
  WISTIA_VIDEO_URL_REGEX,
  YOUTUBE_VIDEO_URL_REGEX,
} from "@/constants/constants";

import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export async function fetcher<JSON = any>(input: RequestInfo, init?: RequestInit): Promise<JSON> {
  const res = await fetch(input, init);

  if (!res.ok) {
    const json = await res.json();
    if (json.error) {
      const error = new Error(json.error) as Error & {
        status: number;
      };
      error.status = res.status;
      throw error;
    } else {
      throw new Error("An unexpected error occurred");
    }
  }

  return res.json();
}

export const capitalize = (value: string): string => {
  if (!value || typeof value !== "string") return value;
  return value.charAt(0).toUpperCase() + value.slice(1);
};

export const truncate = (value: string, length: number): string => {
  if (!value || value.length <= length) return value;
  return `${value.slice(0, length)}...`;
};

export const convertURLToYoutubeVideoId = (url: string): string | null => {
  const match = url.match(YOUTUBE_VIDEO_URL_REGEX);
  return match && match[3].length === 11 ? match[3] : null;
};

export const convertURLToSoundcloudTrackId = (url: string): string | null => {
  const match = url.match(SOUNDCLOUD_TRACK_SHORT_URL_REGEX) || url.match(SOUNDCLOUD_TRACK_URL_REGEX);
  return match?.[2] ? match[2] : null;
};

export const convertURLToVimeoVideoId = (url: string): string | null => {
  const match = url.match(VIMEO_VIDEO_URL_REGEX);
  return match?.[2] ? match[2] : null;
};

export const convertURLToWistiaVideoId = (url: string): string | null => {
  const match = url.match(WISTIA_VIDEO_URL_REGEX);
  return match?.[3] ? match[3] : null;
};

export const convertURLToDailyMotionVideoId = (url: string): string | null => {
  const match = url.match(DAILYMOTION_URL_REGEX);
  return match?.[1] ? match[1] : null;
};

export const convertURLToTwitchVideoId = (url: string): string | null => {
  const match = url.match(TWITCH_URL_REGEX);
  return match?.[1] ? match[1] : null;
};

export const convertURLToCorrectProviderVideoId = (url: string): string | null => {
  if (typeof url !== "string") return null;
  if (convertURLToYoutubeVideoId(url)) {
    return convertURLToYoutubeVideoId(url);
  } else if (convertURLToSoundcloudTrackId(url)) {
    return convertURLToSoundcloudTrackId(url);
  } else if (convertURLToVimeoVideoId(url)) {
    return convertURLToVimeoVideoId(url);
  } else if (convertURLToDailyMotionVideoId(url)) {
    return convertURLToDailyMotionVideoId(url);
  } else if (convertURLToTwitchVideoId(url)) {
    return convertURLToTwitchVideoId(url);
  }
  return null;
};

export const verifyURLIsAcceptedProvider = convertURLToCorrectProviderVideoId;

export const getMediaProviderFromVideoUrl = (
  url: string
): {
  name: "youtube" | "soundcloud" | "vimeo" | "wistia" | "dailymotion" | "twitch" | "mp4" | "mp3";
  url: string;
  id: string | null;
} | null => {
  if (convertURLToYoutubeVideoId(url)) {
    return {
      name: "youtube",
      url: `https://www.youtube.com/oembed?url=${encodeURI(url)}`,
      id: convertURLToYoutubeVideoId(url),
    };
  } else if (convertURLToSoundcloudTrackId(url)) {
    return {
      name: "soundcloud",
      url: `https://soundcloud.com/oembed?format=json&url=${encodeURI(url)}`,
      id: convertURLToSoundcloudTrackId(url),
    };
  } else if (convertURLToVimeoVideoId(url)) {
    return {
      name: "vimeo",
      url: `https://vimeo.com/api/oembed.json?url=${encodeURI(url)}`,
      id: convertURLToVimeoVideoId(url),
    };
  } else if (convertURLToWistiaVideoId(url)) {
    return {
      name: "wistia",
      url: `http://fast.wistia.com/oembed?url=${encodeURI(url)}`,
      id: convertURLToWistiaVideoId(url),
    };
  } else if (convertURLToDailyMotionVideoId(url)) {
    return {
      name: "dailymotion",
      url: `https://www.dailymotion.com/services/oembed?url=${encodeURI(url)}`,
      id: convertURLToDailyMotionVideoId(url),
    };
  } else if (convertURLToTwitchVideoId(url)) {
    return {
      name: "twitch",
      url: `https://iframe.ly/api/oembed?url=${encodeURI(url)}&api_key=2711e95040a356a5d79020`,
      id: convertURLToTwitchVideoId(url),
    };
  } else if (url.match(VIDEO_FILE_URL_REGEX)) {
    return {
      name: "mp4",
      url: `${encodeURI(url)}`,
      id: url,
    };
  } else if (url.match(AUDIO_FILE_URL_REGEX)) {
    // TODO: Not implemented yet
    return {
      name: "mp3",
      url: `${encodeURI(url)}`,
      id: url,
    };
  }
  return null;
};

//Facebook, Streamable, Twitch

export const isValidUrl = (url: string): string | null => {
  if (URL_REGEX.test(url)) return url;
  return null;
};

export const pickBetweenRandomString = (firstItem: string, secondItem: string) => (Math.random() > 0.5 ? firstItem : secondItem);

export const deepEqual = (obj1: any, obj2: any): boolean => {
  if (obj1 === obj2) {
    return true;
  }

  if (typeof obj1 !== "object" || obj1 === null || typeof obj2 !== "object" || obj2 === null) {
    return false;
  }

  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);

  if (keys1.length !== keys2.length) {
    return false;
  }

  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }

  return true;
};

================
File: src/libs/utils/names.ts
================
import { capitalize } from "./frontend-utils";

const getRandomInt = (min: number, max: number) => Math.floor(Math.random() * (max - min)) + min;

export const generateName = () => {
  const name1 = [
    "abandoned",
    "able",
    "absolute",
    "adorable",
    "adventurous",
    "academic",
    "acceptable",
    "acclaimed",
    "accomplished",
    "accurate",
    "aching",
    "acidic",
    "acrobatic",
    "active",
    "actual",
    "adept",
    "admirable",
    "admired",
    "adolescent",
    "adorable",
    "adored",
    "advanced",
    "afraid",
    "affectionate",
    "aged",
    "aggravating",
    "aggressive",
    "agile",
    "agitated",
    "agonizing",
    "agreeable",
    "ajar",
    "alarmed",
    "alarming",
    "alert",
    "alienated",
    "alive",
    "all",
    "altruistic",
    "amazing",
    "ambitious",
    "ample",
    "amused",
    "amusing",
    "anchored",
    "ancient",
    "angelic",
    "angry",
    "anguished",
    "animated",
    "annual",
    "another",
    "antique",
    "anxious",
    "any",
    "apprehensive",
    "appropriate",
    "apt",
    "arctic",
    "arid",
    "aromatic",
    "artistic",
    "ashamed",
    "assured",
    "astonishing",
    "athletic",
    "attached",
    "attentive",
    "attractive",
    "austere",
    "authentic",
    "authorized",
    "automatic",
    "avaricious",
    "average",
    "aware",
    "awesome",
    "awful",
    "awkward",
    "babyish",
    "bad",
    "back",
    "baggy",
    "bare",
    "barren",
    "basic",
    "beautiful",
    "belated",
    "beloved",
    "beneficial",
    "better",
    "best",
    "bewitched",
    "big",
    "big-hearted",
    "biodegradable",
    "bite-sized",
    "bitter",
    "black",
    "black-and-white",
    "bland",
    "blank",
    "blaring",
    "bleak",
    "blind",
    "blissful",
    "blond",
    "blue",
    "blushing",
    "bogus",
    "boiling",
    "bold",
    "bony",
    "boring",
    "bossy",
    "both",
    "bouncy",
    "bountiful",
    "bowed",
    "brave",
    "breakable",
    "brief",
    "bright",
    "brilliant",
    "brisk",
    "broken",
    "bronze",
    "brown",
    "bruised",
    "bubbly",
    "bulky",
    "bumpy",
    "buoyant",
    "burdensome",
    "burly",
    "bustling",
    "busy",
    "buttery",
    "buzzing",
    "calculating",
    "calm",
    "candid",
    "canine",
    "capital",
    "carefree",
    "careful",
    "careless",
    "caring",
    "cautious",
    "cavernous",
    "celebrated",
    "charming",
    "cheap",
    "cheerful",
    "cheery",
    "chief",
    "chilly",
    "chubby",
    "circular",
    "classic",
    "clean",
    "clear",
    "clear-cut",
    "clever",
    "close",
    "closed",
    "cloudy",
    "clueless",
    "clumsy",
    "cluttered",
    "coarse",
    "cold",
    "colorful",
    "colorless",
    "colossal",
    "comfortable",
    "common",
    "compassionate",
    "competent",
    "complete",
    "complex",
    "complicated",
    "composed",
    "concerned",
    "concrete",
    "confused",
    "conscious",
    "considerate",
    "constant",
    "content",
    "conventional",
    "cooked",
    "cool",
    "cooperative",
    "coordinated",
    "corny",
    "corrupt",
    "costly",
    "courageous",
    "courteous",
    "crafty",
    "crazy",
    "creamy",
    "creative",
    "creepy",
    "criminal",
    "crisp",
    "critical",
    "crooked",
    "crowded",
    "cruel",
    "crushing",
    "cuddly",
    "cultivated",
    "cultured",
    "cumbersome",
    "curly",
    "curvy",
    "cute",
    "cylindrical",
    "damaged",
    "damp",
    "dangerous",
    "dapper",
    "daring",
    "darling",
    "dark",
    "dazzling",
    "dead",
    "deadly",
    "deafening",
    "dear",
    "dearest",
    "decent",
    "decimal",
    "decisive",
    "deep",
    "defenseless",
    "defensive",
    "defiant",
    "deficient",
    "definite",
    "definitive",
    "delayed",
    "delectable",
    "delicious",
    "delightful",
    "delirious",
    "demanding",
    "dense",
    "dental",
    "dependable",
    "dependent",
    "descriptive",
    "deserted",
    "detailed",
    "determined",
    "devoted",
    "different",
    "difficult",
    "digital",
    "diligent",
    "dim",
    "dimpled",
    "dimwitted",
    "direct",
    "disastrous",
    "discrete",
    "disfigured",
    "disgusting",
    "disloyal",
    "dismal",
    "distant",
    "downright",
    "dreary",
    "dirty",
    "disguised",
    "dishonest",
    "dismal",
    "distant",
    "distinct",
    "distorted",
    "dizzy",
    "dopey",
    "doting",
    "double",
    "downright",
    "drab",
    "drafty",
    "dramatic",
    "dreary",
    "droopy",
    "dry",
    "dual",
    "dull",
    "dutiful",
    "each",
    "eager",
    "earnest",
    "early",
    "easy",
    "easy-going",
    "ecstatic",
    "edible",
    "educated",
    "elaborate",
    "elastic",
    "elated",
    "elderly",
    "electric",
    "elegant",
    "elementary",
    "elliptical",
    "embarrassed",
    "embellished",
    "eminent",
    "emotional",
    "empty",
    "enchanted",
    "enchanting",
    "energetic",
    "enlightened",
    "enormous",
    "enraged",
    "entire",
    "envious",
    "equal",
    "equatorial",
    "essential",
    "esteemed",
    "ethical",
    "euphoric",
    "even",
    "evergreen",
    "everlasting",
    "every",
    "evil",
    "exalted",
    "excellent",
    "exemplary",
    "exhausted",
    "excitable",
    "excited",
    "exciting",
    "exotic",
    "expensive",
    "experienced",
    "expert",
    "extraneous",
    "extroverted",
    "extra-large",
    "extra-small",
    "fabulous",
    "failing",
    "faint",
    "fair",
    "faithful",
    "fake",
    "false",
    "familiar",
    "famous",
    "fancy",
    "fantastic",
    "far",
    "faraway",
    "far-flung",
    "far-off",
    "fast",
    "fat",
    "fatal",
    "fatherly",
    "favorable",
    "favorite",
    "fearful",
    "fearless",
    "feisty",
    "feline",
    "female",
    "feminine",
    "few",
    "fickle",
    "filthy",
    "fine",
    "finished",
    "firm",
    "first",
    "firsthand",
    "fitting",
    "fixed",
    "flaky",
    "flamboyant",
    "flashy",
    "flat",
    "flawed",
    "flawless",
    "flickering",
    "flimsy",
    "flippant",
    "flowery",
    "fluffy",
    "fluid",
    "flustered",
    "focused",
    "fond",
    "foolhardy",
    "foolish",
    "forceful",
    "forked",
    "formal",
    "forsaken",
    "forthright",
    "fortunate",
    "fragrant",
    "frail",
    "frank",
    "frayed",
    "free",
    "French",
    "fresh",
    "frequent",
    "friendly",
    "frightened",
    "frightening",
    "frigid",
    "frilly",
    "frizzy",
    "frivolous",
    "front",
    "frosty",
    "frozen",
    "frugal",
    "fruitful",
    "full",
    "fumbling",
    "functional",
    "funny",
    "fussy",
    "fuzzy",
    "gargantuan",
    "gaseous",
    "general",
    "generous",
    "gentle",
    "genuine",
    "giant",
    "giddy",
    "gigantic",
    "gifted",
    "giving",
    "glamorous",
    "glaring",
    "glass",
    "gleaming",
    "gleeful",
    "glistening",
    "glittering",
    "gloomy",
    "glorious",
    "glossy",
    "glum",
    "golden",
    "good",
    "good-natured",
    "gorgeous",
    "graceful",
    "gracious",
    "grand",
    "grandiose",
    "granular",
    "grateful",
    "grave",
    "gray",
    "great",
    "greedy",
    "green",
    "gregarious",
    "grim",
    "grimy",
    "gripping",
    "grizzled",
    "gross",
    "grotesque",
    "grouchy",
    "grounded",
    "growing",
    "growling",
    "grown",
    "grubby",
    "gruesome",
    "grumpy",
    "guilty",
    "gullible",
    "gummy",
    "hairy",
    "half",
    "handmade",
    "handsome",
    "handy",
    "happy",
    "happy-go-lucky",
    "hard",
    "hard-to-find",
    "harmful",
    "harmless",
    "harmonious",
    "harsh",
    "hasty",
    "hateful",
    "haunting",
    "healthy",
    "heartfelt",
    "hearty",
    "heavenly",
    "heavy",
    "hefty",
    "helpful",
    "helpless",
    "hidden",
    "hideous",
    "high",
    "high-level",
    "hilarious",
    "hoarse",
    "hollow",
    "homely",
    "honest",
    "honorable",
    "honored",
    "hopeful",
    "horrible",
    "hospitable",
    "hot",
    "huge",
    "humble",
    "humiliating",
    "humming",
    "humongous",
    "hungry",
    "hurtful",
    "husky",
    "icky",
    "icy",
    "ideal",
    "idealistic",
    "identical",
    "idle",
    "idiotic",
    "idolized",
    "ignorant",
    "ill",
    "illegal",
    "ill-fated",
    "ill-informed",
    "illiterate",
    "illustrious",
    "imaginary",
    "imaginative",
    "immaculate",
    "immaterial",
    "immediate",
    "immense",
    "impassioned",
    "impeccable",
    "impartial",
    "imperfect",
    "imperturbable",
    "impish",
    "impolite",
    "important",
    "impossible",
    "impractical",
    "impressionable",
    "impressive",
    "improbable",
    "impure",
    "inborn",
    "incomparable",
    "incompatible",
    "incomplete",
    "inconsequential",
    "incredible",
    "indelible",
    "inexperienced",
    "indolent",
    "infamous",
    "infantile",
    "infatuated",
    "inferior",
    "infinite",
    "informal",
    "innocent",
    "insecure",
    "insidious",
    "insignificant",
    "insistent",
    "instructive",
    "insubstantial",
    "intelligent",
    "intent",
    "intentional",
    "interesting",
    "internal",
    "international",
    "intrepid",
    "ironclad",
    "irresponsible",
    "irritating",
    "itchy",
    "jaded",
    "jagged",
    "jam-packed",
    "jaunty",
    "jealous",
    "jittery",
    "joint",
    "jolly",
    "jovial",
    "joyful",
    "joyous",
    "jubilant",
    "judicious",
    "juicy",
    "jumbo",
    "junior",
    "jumpy",
    "juvenile",
    "kaleidoscopic",
    "keen",
    "key",
    "kind",
    "kindhearted",
    "kindly",
    "klutzy",
    "knobby",
    "knotty",
    "knowledgeable",
    "knowing",
    "known",
    "kooky",
    "kosher",
    "lame",
    "lanky",
    "large",
    "last",
    "lasting",
    "late",
    "lavish",
    "lawful",
    "lazy",
    "leading",
    "lean",
    "leafy",
    "left",
    "legal",
    "legitimate",
    "light",
    "lighthearted",
    "likable",
    "likely",
    "limited",
    "limp",
    "limping",
    "linear",
    "lined",
    "liquid",
    "little",
    "live",
    "lively",
    "livid",
    "loathsome",
    "lone",
    "lonely",
    "long",
    "long-term",
    "loose",
    "lopsided",
    "lost",
    "loud",
    "lovable",
    "lovely",
    "loving",
    "low",
    "loyal",
    "lucky",
    "lumbering",
    "luminous",
    "lumpy",
    "lustrous",
    "luxurious",
    "mad",
    "made-up",
    "magnificent",
    "majestic",
    "major",
    "male",
    "mammoth",
    "married",
    "marvelous",
    "masculine",
    "massive",
    "mature",
    "meager",
    "mealy",
    "mean",
    "measly",
    "meaty",
    "medical",
    "mediocre",
    "medium",
    "meek",
    "mellow",
    "melodic",
    "memorable",
    "menacing",
    "merry",
    "messy",
    "metallic",
    "mild",
    "milky",
    "mindless",
    "miniature",
    "minor",
    "minty",
    "miserable",
    "miserly",
    "misguided",
    "misty",
    "mixed",
    "modern",
    "modest",
    "moist",
    "monstrous",
    "monthly",
    "monumental",
    "moral",
    "mortified",
    "motherly",
    "motionless",
    "mountainous",
    "muddy",
    "muffled",
    "multicolored",
    "mundane",
    "murky",
    "mushy",
    "musty",
    "muted",
    "mysterious",
    "naive",
    "narrow",
    "nasty",
    "natural",
    "naughty",
    "nautical",
    "near",
    "neat",
    "necessary",
    "needy",
    "negative",
    "neglected",
    "negligible",
    "neighboring",
    "nervous",
    "new",
    "next",
    "nice",
    "nifty",
    "nimble",
    "nippy",
    "nocturnal",
    "noisy",
    "nonstop",
    "normal",
    "notable",
    "noted",
    "noteworthy",
    "novel",
    "noxious",
    "numb",
    "nutritious",
    "nutty",
    "obedient",
    "obese",
    "oblong",
    "oily",
    "oblong",
    "obvious",
    "occasional",
    "odd",
    "oddball",
    "offbeat",
    "offensive",
    "official",
    "old",
    "old-fashioned",
    "only",
    "open",
    "optimal",
    "optimistic",
    "opulent",
    "orange",
    "orderly",
    "organic",
    "ornate",
    "ornery",
    "ordinary",
    "original",
    "other",
    "our",
    "outlying",
    "outgoing",
    "outlandish",
    "outrageous",
    "outstanding",
    "oval",
    "overcooked",
    "overdue",
    "overjoyed",
    "overlooked",
    "palatable",
    "pale",
    "paltry",
    "parallel",
    "parched",
    "partial",
    "passionate",
    "past",
    "pastel",
    "peaceful",
    "peppery",
    "perfect",
    "perfumed",
    "periodic",
    "perky",
    "personal",
    "pertinent",
    "pesky",
    "pessimistic",
    "petty",
    "phony",
    "physical",
    "piercing",
    "pink",
    "pitiful",
    "plain",
    "plaintive",
    "plastic",
    "playful",
    "pleasant",
    "pleased",
    "pleasing",
    "plump",
    "plush",
    "polished",
    "polite",
    "political",
    "pointed",
    "pointless",
    "poised",
    "poor",
    "popular",
    "portly",
    "posh",
    "positive",
    "possible",
    "potable",
    "powerful",
    "powerless",
    "practical",
    "precious",
    "present",
    "prestigious",
    "pretty",
    "precious",
    "previous",
    "pricey",
    "prickly",
    "primary",
    "prime",
    "pristine",
    "private",
    "prize",
    "probable",
    "productive",
    "profitable",
    "profuse",
    "proper",
    "proud",
    "prudent",
    "punctual",
    "pungent",
    "puny",
    "pure",
    "purple",
    "pushy",
    "putrid",
    "puzzled",
    "puzzling",
    "quaint",
    "qualified",
    "quarrelsome",
    "quarterly",
    "queasy",
    "querulous",
    "questionable",
    "quick",
    "quick-witted",
    "quiet",
    "quintessential",
    "quirky",
    "quixotic",
    "quizzical",
    "radiant",
    "ragged",
    "rapid",
    "rare",
    "rash",
    "raw",
    "recent",
    "reckless",
    "rectangular",
    "ready",
    "real",
    "realistic",
    "reasonable",
    "red",
    "reflecting",
    "regal",
    "regular",
    "reliable",
    "relieved",
    "remarkable",
    "remorseful",
    "remote",
    "repentant",
    "required",
    "respectful",
    "responsible",
    "repulsive",
    "revolving",
    "rewarding",
    "rich",
    "rigid",
    "right",
    "ringed",
    "ripe",
    "roasted",
    "robust",
    "rosy",
    "rotating",
    "rotten",
    "rough",
    "round",
    "rowdy",
    "royal",
    "rubbery",
    "rundown",
    "ruddy",
    "rude",
    "runny",
    "rural",
    "rusty",
    "sad",
    "safe",
    "salty",
    "same",
    "sandy",
    "sane",
    "sarcastic",
    "sardonic",
    "satisfied",
    "scaly",
    "scarce",
    "scared",
    "scary",
    "scented",
    "scholarly",
    "scientific",
    "scornful",
    "scratchy",
    "scrawny",
    "second",
    "secondary",
    "second-hand",
    "secret",
    "self-assured",
    "self-reliant",
    "selfish",
    "sentimental",
    "separate",
    "serene",
    "serious",
    "serpentine",
    "several",
    "severe",
    "shabby",
    "shadowy",
    "shady",
    "shallow",
    "shameful",
    "shameless",
    "sharp",
    "shimmering",
    "shiny",
    "shocked",
    "shocking",
    "shoddy",
    "short",
    "short-term",
    "showy",
    "shrill",
    "shy",
    "sick",
    "silent",
    "silky",
    "silly",
    "silver",
    "similar",
    "simple",
    "simplistic",
    "sinful",
    "single",
    "sizzling",
    "skeletal",
    "skinny",
    "sleepy",
    "slight",
    "slim",
    "slimy",
    "slippery",
    "slow",
    "slushy",
    "small",
    "smart",
    "smoggy",
    "smooth",
    "smug",
    "snappy",
    "snarling",
    "sneaky",
    "sniveling",
    "snoopy",
    "sociable",
    "soft",
    "soggy",
    "solid",
    "somber",
    "some",
    "spherical",
    "sophisticated",
    "sore",
    "sorrowful",
    "soulful",
    "soupy",
    "sour",
    "Spanish",
    "sparkling",
    "sparse",
    "specific",
    "spectacular",
    "speedy",
    "spicy",
    "spiffy",
    "spirited",
    "spiteful",
    "splendid",
    "spotless",
    "spotted",
    "spry",
    "square",
    "squeaky",
    "squiggly",
    "stable",
    "staid",
    "stained",
    "stale",
    "standard",
    "starchy",
    "stark",
    "starry",
    "steep",
    "sticky",
    "stiff",
    "stimulating",
    "stingy",
    "stormy",
    "straight",
    "strange",
    "steel",
    "strict",
    "strident",
    "striking",
    "striped",
    "strong",
    "studious",
    "stunning",
    "stupendous",
    "stupid",
    "sturdy",
    "stylish",
    "subdued",
    "submissive",
    "substantial",
    "subtle",
    "suburban",
    "sudden",
    "sugary",
    "sunny",
    "super",
    "superb",
    "superficial",
    "superior",
    "supportive",
    "sure-footed",
    "surprised",
    "suspicious",
    "svelte",
    "sweaty",
    "sweet",
    "sweltering",
    "swift",
    "sympathetic",
    "tall",
    "talkative",
    "tame",
    "tan",
    "tangible",
    "tart",
    "tasty",
    "tattered",
    "taut",
    "tedious",
    "teeming",
    "tempting",
    "tender",
    "tense",
    "tepid",
    "terrible",
    "terrific",
    "testy",
    "thankful",
    "that",
    "these",
    "thick",
    "thin",
    "third",
    "thirsty",
    "this",
    "thorough",
    "thorny",
    "those",
    "thoughtful",
    "threadbare",
    "thrifty",
    "thunderous",
    "tidy",
    "tight",
    "timely",
    "tinted",
    "tiny",
    "tired",
    "torn",
    "total",
    "tough",
    "traumatic",
    "treasured",
    "tremendous",
    "tragic",
    "trained",
    "tremendous",
    "triangular",
    "tricky",
    "trifling",
    "trim",
    "trivial",
    "troubled",
    "true",
    "trusting",
    "trustworthy",
    "trusty",
    "truthful",
    "tubby",
    "turbulent",
    "twin",
    "ugly",
    "ultimate",
    "unacceptable",
    "unaware",
    "uncomfortable",
    "uncommon",
    "unconscious",
    "understated",
    "unequaled",
    "uneven",
    "unfinished",
    "unfit",
    "unfolded",
    "unfortunate",
    "unhappy",
    "unhealthy",
    "uniform",
    "unimportant",
    "unique",
    "united",
    "unkempt",
    "unknown",
    "unlawful",
    "unlined",
    "unlucky",
    "unnatural",
    "unpleasant",
    "unrealistic",
    "unripe",
    "unruly",
    "unselfish",
    "unsightly",
    "unsteady",
    "unsung",
    "untidy",
    "untimely",
    "untried",
    "untrue",
    "unused",
    "unusual",
    "unwelcome",
    "unwieldy",
    "unwilling",
    "unwitting",
    "unwritten",
    "upbeat",
    "upright",
    "upset",
    "urban",
    "usable",
    "used",
    "useful",
    "useless",
    "utilized",
    "utter",
    "vacant",
    "vague",
    "vain",
    "valid",
    "valuable",
    "vapid",
    "variable",
    "vast",
    "velvety",
    "venerated",
    "vengeful",
    "verifiable",
    "vibrant",
    "vicious",
    "victorious",
    "vigilant",
    "vigorous",
    "villainous",
    "violet",
    "violent",
    "virtual",
    "virtuous",
    "visible",
    "vital",
    "vivacious",
    "vivid",
    "voluminous",
    "wan",
    "warlike",
    "warm",
    "warmhearted",
    "warped",
    "wary",
    "wasteful",
    "watchful",
    "waterlogged",
    "watery",
    "wavy",
    "wealthy",
    "weak",
    "weary",
    "webbed",
    "wee",
    "weekly",
    "weepy",
    "weighty",
    "weird",
    "welcome",
    "well-documented",
    "well-groomed",
    "well-informed",
    "well-lit",
    "well-made",
    "well-off",
    "well-to-do",
    "well-worn",
    "wet",
    "which",
    "whimsical",
    "whirlwind",
    "whispered",
    "white",
    "whole",
    "whopping",
    "wicked",
    "wide",
    "wide-eyed",
    "wiggly",
    "wild",
    "willing",
    "wilted",
    "winding",
    "windy",
    "winged",
    "wiry",
    "wise",
    "witty",
    "wobbly",
    "woeful",
    "wonderful",
    "wooden",
    "woozy",
    "wordy",
    "worldly",
    "worn",
    "worried",
    "worrisome",
    "worse",
    "worst",
    "worthless",
    "worthwhile",
    "worthy",
    "wrathful",
    "wretched",
    "writhing",
    "wrong",
    "wry",
    "yawning",
    "yearly",
    "yellow",
    "yellowish",
    "young",
    "youthful",
    "yummy",
    "zany",
    "zealous",
    "zesty",
    "zigzag",
    "rocky",
  ];

  var name2 = [
    "people",
    "history",
    "way",
    "art",
    "world",
    "information",
    "map",
    "family",
    "government",
    "health",
    "system",
    "computer",
    "meat",
    "year",
    "thanks",
    "music",
    "person",
    "reading",
    "method",
    "data",
    "food",
    "understanding",
    "theory",
    "law",
    "bird",
    "literature",
    "problem",
    "software",
    "control",
    "knowledge",
    "power",
    "ability",
    "economics",
    "love",
    "internet",
    "television",
    "science",
    "library",
    "nature",
    "fact",
    "product",
    "idea",
    "temperature",
    "investment",
    "area",
    "society",
    "activity",
    "story",
    "industry",
    "media",
    "thing",
    "oven",
    "community",
    "definition",
    "safety",
    "quality",
    "development",
    "language",
    "management",
    "player",
    "variety",
    "video",
    "week",
    "security",
    "country",
    "exam",
    "movie",
    "organization",
    "equipment",
    "physics",
    "analysis",
    "policy",
    "series",
    "thought",
    "basis",
    "boyfriend",
    "direction",
    "strategy",
    "technology",
    "army",
    "camera",
    "freedom",
    "paper",
    "environment",
    "child",
    "instance",
    "month",
    "truth",
    "marketing",
    "university",
    "writing",
    "article",
    "department",
    "difference",
    "goal",
    "news",
    "audience",
    "fishing",
    "growth",
    "income",
    "marriage",
    "user",
    "combination",
    "failure",
    "meaning",
    "medicine",
    "philosophy",
    "teacher",
    "communication",
    "night",
    "chemistry",
    "disease",
    "disk",
    "energy",
    "nation",
    "road",
    "role",
    "soup",
    "advertising",
    "location",
    "success",
    "addition",
    "apartment",
    "education",
    "math",
    "moment",
    "painting",
    "politics",
    "attention",
    "decision",
    "event",
    "property",
    "shopping",
    "student",
    "wood",
    "competition",
    "distribution",
    "entertainment",
    "office",
    "population",
    "president",
    "unit",
    "category",
    "cigarette",
    "context",
    "introduction",
    "opportunity",
    "performance",
    "driver",
    "flight",
    "length",
    "magazine",
    "newspaper",
    "relationship",
    "teaching",
    "cell",
    "dealer",
    "debate",
    "finding",
    "lake",
    "member",
    "message",
    "phone",
    "scene",
    "appearance",
    "association",
    "concept",
    "customer",
    "death",
    "discussion",
    "housing",
    "inflation",
    "insurance",
    "mood",
    "woman",
    "advice",
    "blood",
    "effort",
    "expression",
    "importance",
    "opinion",
    "payment",
    "reality",
    "responsibility",
    "situation",
    "skill",
    "statement",
    "wealth",
    "application",
    "city",
    "county",
    "depth",
    "estate",
    "foundation",
    "grandmother",
    "heart",
    "perspective",
    "photo",
    "recipe",
    "studio",
    "topic",
    "collection",
    "depression",
    "imagination",
    "passion",
    "percentage",
    "resource",
    "setting",
    "ad",
    "agency",
    "college",
    "connection",
    "criticism",
    "debt",
    "description",
    "memory",
    "patience",
    "secretary",
    "solution",
    "administration",
    "aspect",
    "attitude",
    "director",
    "personality",
    "psychology",
    "recommendation",
    "response",
    "selection",
    "storage",
    "version",
    "alcohol",
    "argument",
    "complaint",
    "contract",
    "emphasis",
    "highway",
    "loss",
    "membership",
    "possession",
    "preparation",
    "steak",
    "union",
    "agreement",
    "cancer",
    "currency",
    "employment",
    "engineering",
    "entry",
    "interaction",
    "limit",
    "mixture",
    "preference",
    "region",
    "republic",
    "seat",
    "tradition",
    "virus",
    "actor",
    "classroom",
    "delivery",
    "device",
    "difficulty",
    "drama",
    "election",
    "engine",
    "football",
    "guidance",
    "hotel",
    "match",
    "owner",
    "priority",
    "protection",
    "suggestion",
    "tension",
    "variation",
    "anxiety",
    "atmosphere",
    "awareness",
    "bread",
    "climate",
    "comparison",
    "confusion",
    "construction",
    "elevator",
    "emotion",
    "employee",
    "employer",
    "guest",
    "height",
    "leadership",
    "mall",
    "manager",
    "operation",
    "recording",
    "respect",
    "sample",
    "transportation",
    "boring",
    "charity",
    "cousin",
    "disaster",
    "editor",
    "efficiency",
    "excitement",
    "extent",
    "feedback",
    "guitar",
    "homework",
    "leader",
    "mom",
    "outcome",
    "permission",
    "presentation",
    "promotion",
    "reflection",
    "refrigerator",
    "resolution",
    "revenue",
    "session",
    "singer",
    "tennis",
    "basket",
    "bonus",
    "cabinet",
    "childhood",
    "church",
    "clothes",
    "coffee",
    "dinner",
    "drawing",
    "hair",
    "hearing",
    "initiative",
    "judgment",
    "lab",
    "measurement",
    "mode",
    "mud",
    "orange",
    "poetry",
    "police",
    "possibility",
    "procedure",
    "queen",
    "ratio",
    "relation",
    "restaurant",
    "satisfaction",
    "sector",
    "signature",
    "significance",
    "song",
    "tooth",
    "town",
    "vehicle",
    "volume",
    "wife",
    "accident",
    "airport",
    "appointment",
    "arrival",
    "assumption",
    "baseball",
    "chapter",
    "committee",
    "conversation",
    "database",
    "enthusiasm",
    "error",
    "explanation",
    "farmer",
    "gate",
    "girl",
    "hall",
    "historian",
    "hospital",
    "injury",
    "instruction",
    "maintenance",
    "manufacturer",
    "meal",
    "perception",
    "pie",
    "poem",
    "presence",
    "proposal",
    "reception",
    "replacement",
    "revolution",
    "river",
    "son",
    "speech",
    "tea",
    "village",
    "warning",
    "winner",
    "worker",
    "writer",
    "assistance",
    "breath",
    "buyer",
    "chest",
    "chocolate",
    "conclusion",
    "contribution",
    "cookie",
    "courage",
    "desk",
    "drawer",
    "establishment",
    "examination",
    "garbage",
    "grocery",
    "honey",
    "impression",
    "improvement",
    "independence",
    "insect",
    "inspection",
    "inspector",
    "king",
    "ladder",
    "menu",
    "penalty",
    "piano",
    "potato",
    "profession",
    "professor",
    "quantity",
    "reaction",
    "requirement",
    "salad",
    "sister",
    "supermarket",
    "tongue",
    "weakness",
    "wedding",
    "affair",
    "ambition",
    "analyst",
    "apple",
    "assignment",
    "assistant",
    "bathroom",
    "bedroom",
    "beer",
    "birthday",
    "celebration",
    "championship",
    "cheek",
    "client",
    "consequence",
    "departure",
    "diamond",
    "dirt",
    "ear",
    "fortune",
    "friendship",
    "funeral",
    "gene",
    "girlfriend",
    "hat",
    "indication",
    "intention",
    "lady",
    "midnight",
    "negotiation",
    "obligation",
    "passenger",
    "pizza",
    "platform",
    "poet",
    "pollution",
    "recognition",
    "reputation",
    "shirt",
    "speaker",
    "stranger",
    "surgery",
    "sympathy",
    "tale",
    "throat",
    "trainer",
    "uncle",
    "youth",
    "time",
    "work",
    "film",
    "water",
    "money",
    "example",
    "while",
    "business",
    "study",
    "game",
    "life",
    "form",
    "air",
    "day",
    "place",
    "number",
    "part",
    "field",
    "fish",
    "back",
    "process",
    "heat",
    "hand",
    "experience",
    "job",
    "book",
    "end",
    "point",
    "type",
    "home",
    "economy",
    "value",
    "body",
    "market",
    "guide",
    "interest",
    "state",
    "radio",
    "course",
    "company",
    "price",
    "size",
    "card",
    "list",
    "mind",
    "trade",
    "line",
    "care",
    "group",
    "risk",
    "word",
    "fat",
    "force",
    "key",
    "light",
    "training",
    "name",
    "school",
    "top",
    "amount",
    "level",
    "order",
    "practice",
    "research",
    "sense",
    "service",
    "piece",
    "web",
    "boss",
    "sport",
    "fun",
    "house",
    "page",
    "term",
    "test",
    "answer",
    "sound",
    "focus",
    "matter",
    "kind",
    "soil",
    "board",
    "oil",
    "picture",
    "access",
    "garden",
    "range",
    "rate",
    "reason",
    "future",
    "site",
    "demand",
    "exercise",
    "image",
    "case",
    "cause",
    "coast",
    "action",
    "age",
    "bad",
    "boat",
    "record",
    "result",
    "section",
    "building",
    "mouse",
    "cash",
    "class",
    "period",
    "plan",
    "store",
    "tax",
    "side",
    "subject",
    "space",
    "rule",
    "stock",
    "weather",
    "chance",
    "figure",
    "man",
    "model",
    "source",
    "beginning",
    "earth",
    "program",
    "chicken",
    "design",
    "feature",
    "head",
    "material",
    "purpose",
    "question",
    "rock",
    "salt",
    "act",
    "birth",
    "car",
    "dog",
    "object",
    "scale",
    "sun",
    "note",
    "profit",
    "rent",
    "speed",
    "style",
    "war",
    "bank",
    "craft",
    "half",
    "inside",
    "outside",
    "standard",
    "bus",
    "exchange",
    "eye",
    "fire",
    "position",
    "pressure",
    "stress",
    "advantage",
    "benefit",
    "box",
    "frame",
    "issue",
    "step",
    "cycle",
    "face",
    "item",
    "metal",
    "paint",
    "review",
    "room",
    "screen",
    "structure",
    "view",
    "account",
    "ball",
    "discipline",
    "medium",
    "share",
    "balance",
    "bit",
    "black",
    "bottom",
    "choice",
    "gift",
    "impact",
    "machine",
    "shape",
    "tool",
    "wind",
    "address",
    "average",
    "career",
    "culture",
    "morning",
    "pot",
    "sign",
    "table",
    "task",
    "condition",
    "contact",
    "credit",
    "egg",
    "hope",
    "ice",
    "network",
    "north",
    "square",
    "attempt",
    "date",
    "effect",
    "link",
    "post",
    "star",
    "voice",
    "capital",
    "challenge",
    "friend",
    "self",
    "shot",
    "brush",
    "couple",
    "exit",
    "front",
    "function",
    "lack",
    "living",
    "plant",
    "plastic",
    "spot",
    "summer",
    "taste",
    "theme",
    "track",
    "wing",
    "brain",
    "button",
    "click",
    "desire",
    "foot",
    "gas",
    "influence",
    "notice",
    "rain",
    "wall",
    "base",
    "damage",
    "distance",
    "feeling",
    "pair",
    "savings",
    "staff",
    "sugar",
    "target",
    "text",
    "animal",
    "author",
    "budget",
    "discount",
    "file",
    "ground",
    "lesson",
    "minute",
    "officer",
    "phase",
    "reference",
    "register",
    "sky",
    "stage",
    "stick",
    "title",
    "trouble",
    "bowl",
    "bridge",
    "campaign",
    "character",
    "club",
    "edge",
    "evidence",
    "fan",
    "letter",
    "lock",
    "maximum",
    "novel",
    "option",
    "pack",
    "park",
    "quarter",
    "skin",
    "sort",
    "weight",
    "baby",
    "background",
    "carry",
    "dish",
    "factor",
    "fruit",
    "glass",
    "joint",
    "master",
    "muscle",
    "red",
    "strength",
    "traffic",
    "trip",
    "vegetable",
    "appeal",
    "chart",
    "gear",
    "ideal",
    "kitchen",
    "land",
    "log",
    "mother",
    "net",
    "party",
    "principle",
    "relative",
    "sale",
    "season",
    "signal",
    "spirit",
    "street",
    "tree",
    "wave",
    "belt",
    "bench",
    "commission",
    "copy",
    "drop",
    "minimum",
    "path",
    "progress",
    "project",
    "sea",
    "south",
    "status",
    "stuff",
    "ticket",
    "tour",
    "angle",
    "blue",
    "breakfast",
    "confidence",
    "daughter",
    "degree",
    "doctor",
    "dot",
    "dream",
    "duty",
    "essay",
    "father",
    "fee",
    "finance",
    "hour",
    "juice",
    "luck",
    "milk",
    "mouth",
    "peace",
    "pipe",
    "stable",
    "storm",
    "substance",
    "team",
    "trick",
    "afternoon",
    "bat",
    "beach",
    "blank",
    "catch",
    "chain",
    "consideration",
    "cream",
    "crew",
    "detail",
    "gold",
    "interview",
    "kid",
    "mark",
    "mission",
    "pain",
    "pleasure",
    "score",
    "screw",
    "sex",
    "shop",
    "shower",
    "suit",
    "tone",
    "window",
    "agent",
    "band",
    "bath",
    "block",
    "bone",
    "calendar",
    "candidate",
    "cap",
    "coat",
    "contest",
    "corner",
    "court",
    "cup",
    "district",
    "door",
    "east",
    "finger",
    "garage",
    "guarantee",
    "hole",
    "hook",
    "implement",
    "layer",
    "lecture",
    "lie",
    "manner",
    "meeting",
    "nose",
    "parking",
    "partner",
    "profile",
    "rice",
    "routine",
    "schedule",
    "swimming",
    "telephone",
    "tip",
    "winter",
    "airline",
    "bag",
    "battle",
    "bed",
    "bill",
    "bother",
    "cake",
    "code",
    "curve",
    "designer",
    "dimension",
    "dress",
    "ease",
    "emergency",
    "evening",
    "extension",
    "farm",
    "fight",
    "gap",
    "grade",
    "holiday",
    "horror",
    "horse",
    "host",
    "husband",
    "loan",
    "mistake",
    "mountain",
    "nail",
    "noise",
    "occasion",
    "package",
    "patient",
    "pause",
    "phrase",
    "proof",
    "race",
    "relief",
    "sand",
    "sentence",
    "shoulder",
    "smoke",
    "stomach",
    "string",
    "tourist",
    "towel",
    "vacation",
    "west",
    "wheel",
    "wine",
    "arm",
    "aside",
    "associate",
    "bet",
    "blow",
    "border",
    "branch",
    "breast",
    "brother",
    "buddy",
    "bunch",
    "chip",
    "coach",
    "cross",
    "document",
    "draft",
    "dust",
    "expert",
    "floor",
    "god",
    "golf",
    "habit",
    "iron",
    "judge",
    "knife",
    "landscape",
    "league",
    "mail",
    "mess",
    "native",
    "opening",
    "parent",
    "pattern",
    "pin",
    "pool",
    "pound",
    "request",
    "salary",
    "shame",
    "shelter",
    "shoe",
    "silver",
    "tackle",
    "tank",
    "trust",
    "assist",
    "bake",
    "bar",
    "bell",
    "bike",
    "blame",
    "boy",
    "brick",
    "chair",
    "closet",
    "clue",
    "collar",
    "comment",
    "conference",
    "devil",
    "diet",
    "fear",
    "fuel",
    "glove",
    "jacket",
    "lunch",
    "monitor",
    "mortgage",
    "nurse",
    "pace",
    "panic",
    "peak",
    "plane",
    "reward",
    "row",
    "sandwich",
    "shock",
    "spite",
    "spray",
    "surprise",
    "till",
    "transition",
    "weekend",
    "welcome",
    "yard",
    "alarm",
    "bend",
    "bicycle",
    "bite",
    "blind",
    "bottle",
    "cable",
    "candle",
    "clerk",
    "cloud",
    "concert",
    "counter",
    "flower",
    "grandfather",
    "harm",
    "knee",
    "lawyer",
    "leather",
    "load",
    "mirror",
    "neck",
    "pension",
    "plate",
    "purple",
    "ruin",
    "ship",
    "skirt",
    "slice",
    "snow",
    "specialist",
    "stroke",
    "switch",
    "trash",
    "tune",
    "zone",
    "anger",
    "award",
    "bid",
    "bitter",
    "boot",
    "bug",
    "camp",
    "candy",
    "carpet",
    "cat",
    "champion",
    "channel",
    "clock",
    "comfort",
    "cow",
    "crack",
    "engineer",
    "entrance",
    "fault",
    "grass",
    "guy",
    "hell",
    "highlight",
    "incident",
    "island",
    "joke",
    "jury",
    "leg",
    "lip",
    "mate",
    "motor",
    "nerve",
    "passage",
    "pen",
    "pride",
    "priest",
    "prize",
    "promise",
    "resident",
    "resort",
    "ring",
    "roof",
    "rope",
    "sail",
    "scheme",
    "script",
    "sock",
    "station",
    "toe",
    "tower",
    "truck",
    "witness",
    "can",
    "will",
    "other",
    "use",
    "make",
    "good",
    "look",
    "help",
    "go",
    "great",
    "being",
    "still",
    "public",
    "read",
    "keep",
    "start",
    "give",
    "human",
    "local",
    "general",
    "specific",
    "long",
    "play",
    "feel",
    "high",
    "put",
    "common",
    "set",
    "change",
    "simple",
    "past",
    "big",
    "possible",
    "particular",
    "major",
    "personal",
    "current",
    "national",
    "cut",
    "natural",
    "physical",
    "show",
    "try",
    "check",
    "second",
    "call",
    "move",
    "pay",
    "let",
    "increase",
    "single",
    "individual",
    "turn",
    "ask",
    "buy",
    "guard",
    "hold",
    "main",
    "offer",
    "potential",
    "professional",
    "international",
    "travel",
    "cook",
    "alternative",
    "special",
    "working",
    "whole",
    "dance",
    "excuse",
    "cold",
    "commercial",
    "low",
    "purchase",
    "deal",
    "primary",
    "worth",
    "fall",
    "necessary",
    "positive",
    "produce",
    "search",
    "present",
    "spend",
    "talk",
    "creative",
    "tell",
    "cost",
    "drive",
    "green",
    "support",
    "glad",
    "remove",
    "return",
    "run",
    "complex",
    "due",
    "effective",
    "middle",
    "regular",
    "reserve",
    "independent",
    "leave",
    "original",
    "reach",
    "rest",
    "serve",
    "watch",
    "beautiful",
    "charge",
    "active",
    "break",
    "negative",
    "safe",
    "stay",
    "visit",
    "visual",
    "affect",
    "cover",
    "report",
    "rise",
    "walk",
    "white",
    "junior",
    "pick",
    "unique",
    "classic",
    "final",
    "lift",
    "mix",
    "private",
    "stop",
    "teach",
    "western",
    "concern",
    "familiar",
    "fly",
    "official",
    "broad",
    "comfortable",
    "gain",
    "rich",
    "save",
    "stand",
    "young",
    "heavy",
    "lead",
    "listen",
    "valuable",
    "worry",
    "handle",
    "leading",
    "meet",
    "release",
    "sell",
    "finish",
    "normal",
    "press",
    "ride",
    "secret",
    "spread",
    "spring",
    "tough",
    "wait",
    "brown",
    "deep",
    "display",
    "flow",
    "hit",
    "objective",
    "shoot",
    "touch",
    "cancel",
    "chemical",
    "cry",
    "dump",
    "extreme",
    "push",
    "conflict",
    "eat",
    "fill",
    "formal",
    "jump",
    "kick",
    "opposite",
    "pass",
    "pitch",
    "remote",
    "total",
    "treat",
    "vast",
    "abuse",
    "beat",
    "burn",
    "deposit",
    "print",
    "raise",
    "sleep",
    "somewhere",
    "advance",
    "consist",
    "dark",
    "double",
    "draw",
    "equal",
    "fix",
    "hire",
    "internal",
    "join",
    "kill",
    "sensitive",
    "tap",
    "win",
    "attack",
    "claim",
    "constant",
    "drag",
    "drink",
    "guess",
    "minor",
    "pull",
    "raw",
    "soft",
    "solid",
    "wear",
    "weird",
    "wonder",
    "annual",
    "count",
    "dead",
    "doubt",
    "feed",
    "forever",
    "impress",
    "repeat",
    "round",
    "sing",
    "slide",
    "strip",
    "wish",
    "combine",
    "command",
    "dig",
    "divide",
    "equivalent",
    "hang",
    "hunt",
    "initial",
    "march",
    "mention",
    "spiritual",
    "survey",
    "tie",
    "adult",
    "brief",
    "crazy",
    "escape",
    "gather",
    "hate",
    "prior",
    "repair",
    "rough",
    "sad",
    "scratch",
    "sick",
    "strike",
    "employ",
    "external",
    "hurt",
    "illegal",
    "laugh",
    "lay",
    "mobile",
    "nasty",
    "ordinary",
    "respond",
    "royal",
    "senior",
    "split",
    "strain",
    "struggle",
    "swim",
    "train",
    "upper",
    "wash",
    "yellow",
    "convert",
    "crash",
    "dependent",
    "fold",
    "funny",
    "grab",
    "hide",
    "miss",
    "permit",
    "quote",
    "recover",
    "resolve",
    "roll",
    "sink",
    "slip",
    "spare",
    "suspect",
    "sweet",
    "swing",
    "twist",
    "upstairs",
    "usual",
    "abroad",
    "brave",
    "calm",
    "concentrate",
    "estimate",
    "grand",
    "male",
    "mine",
    "prompt",
    "quiet",
    "refuse",
    "regret",
    "reveal",
    "rush",
    "shake",
    "shift",
    "shine",
    "steal",
    "suck",
    "surround",
    "bear",
    "brilliant",
    "dare",
    "dear",
    "delay",
    "drunk",
    "female",
    "hurry",
    "inevitable",
    "invite",
    "kiss",
    "neat",
    "pop",
    "punch",
    "quit",
    "reply",
    "representative",
    "resist",
    "rip",
    "rub",
    "silly",
    "smile",
    "spell",
    "stretch",
    "stupid",
    "tear",
    "temporary",
    "tomorrow",
    "wake",
    "wrap",
    "yesterday",
    "Thomas",
    "Tom",
    "Lieuwe",
  ];

  const name = capitalize(name1[getRandomInt(0, name1.length + 1)]) + " " + capitalize(name2[getRandomInt(0, name2.length + 1)]);
  return name;
};

================
File: src/libs/utils/socket.ts
================
import { UserId } from "@/types/interfaces";

export const runIfAuthorized = (host: UserId, userId: UserId | undefined, callback?: () => void, isAdmin?: boolean, disableAdminCheck = false) => {
  if ((isAdmin && !disableAdminCheck) || host === userId) {
    typeof callback === "function" && callback();
  }
};

================
File: src/libs/utils/video-fetch-lib.ts
================
import { BASE_URL } from "@/constants/constants";
import { fetcher, getMediaProviderFromVideoUrl } from "./frontend-utils";

export interface MediaData {
  url: string;
  title: string;
  thumbnail: string;
  id: string;
}

export interface OEmbed {
  version: number;
  type: string;
  provider_name: string;
  provider_url: string;
  height: number;
  width: string;
  title: string;
  description: string;
  thumbnail_url: string;
  html: string;
  author_name: string;
  author_url: string;
}

export const fetchMediaData = async (url: string): Promise<MediaData | null> => {
  let newMedia: MediaData | null = null;

  const mediaProvider = getMediaProviderFromVideoUrl(url);
  if (!mediaProvider) return null;

  if (mediaProvider.name === "mp4") {
    await fetcher(`${BASE_URL}/api/video-proxy?url=${encodeURI(url)}`)
      .then((data: MediaData) => {
        if (data) {
          newMedia = data;
          return newMedia;
        }
      })
      .catch((error) => {
        console.error(`Error fetching ${mediaProvider.name} media details: `, error);
      });

    return newMedia;
  }

  await fetcher(mediaProvider.url)
    .then((data: OEmbed) => {
      newMedia = {
        url,
        title: data.title,
        thumbnail: data.thumbnail_url || "",
        id: mediaProvider.id as string,
      };

      if (mediaProvider.name === "youtube") {
        newMedia.thumbnail = `https://i.ytimg.com/vi/${mediaProvider.id}/maxresdefault.jpg`;
      }
    })
    .catch((error) => {
      console.error(`Error fetching ${mediaProvider.name} media details: `, error);
    });

  return newMedia;
};

// function fetchFacebookEmbed() {
//   const accessToken = "YOUR_FACEBOOK_ACCESS_TOKEN";
//   const facebookPostUrl = "https://www.facebook.com/FacebookPage/posts/POST_ID";
//   const oEmbedUrl = `https://graph.facebook.com/v11.0/oembed_page?url=${encodeURIComponent(facebookPostUrl)}&access_token=${accessToken}`;
// }

================
File: src/mock/mockMessages.json
================
[
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "536512ed-8765-4448-ab3e-41b19e0364f6",
        "timestamp": "2023-07-28T15:27:17.630Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "22312e14-fef1-45c6-bb37-c5bd66955740",
        "timestamp": "2023-07-28T15:27:18.254Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "eb67e014-7888-409e-860a-ec325868890f",
        "timestamp": "2023-07-28T15:27:18.685Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "5afcba92-f775-497f-82e8-ed5ea198130c",
        "timestamp": "2023-07-28T15:27:19.014Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "41eb7ff2-4751-41d8-afa4-200fe3196702",
        "timestamp": "2023-07-28T15:27:19.262Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "f3ce1132-c40f-42bf-897e-112a517a238c",
        "timestamp": "2023-07-28T15:27:19.466Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "3f84a031-f204-43dd-b722-0ec53b66b95c",
        "timestamp": "2023-07-28T15:27:19.764Z"
    },
    {
        "username": "Purple Deep",
        "message": "Test",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "30a21570-8b99-457e-bb55-6f3d95385a03",
        "timestamp": "2023-07-28T15:27:19.961Z"
    },
    {
        "username": "Purple Deep",
        "message": "dadasd",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "23a01f3a-0765-4a2c-8e26-52a28811cc75",
        "timestamp": "2023-07-28T15:27:20.474Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "33c98686-fa2c-4069-844f-2cb37b98160f",
        "timestamp": "2023-07-28T15:27:32.347Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "5d0e2b6d-600e-4e0a-8ad6-ec46bf80d5f9",
        "timestamp": "2023-07-28T15:27:33.299Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "f3517e59-eb72-4fa8-bf40-0b2dd8db5d0c",
        "timestamp": "2023-07-28T15:27:33.963Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "c87ab58c-2348-4f55-a46b-0281fa5e7a91",
        "timestamp": "2023-07-28T15:27:34.325Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "df8144ac-8b21-4368-a2b0-d96412aa7eaa",
        "timestamp": "2023-07-28T15:27:34.656Z"
    },
    {
        "username": "Purple Deep",
        "message": "Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat. Duis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat cupidatat non proident, sunt in culpa qui officia deserunt mollit anim id est laborum.",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "7a8695bc-f71d-45c1-bcea-f1ae52e56c18",
        "timestamp": "2023-07-28T15:27:34.955Z"
    },
    {
        "username": "Purple Deep",
        "message": "dasdas",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "f69cf619-17a5-4cef-8233-07a530a32255",
        "timestamp": "2023-07-28T15:27:59.033Z"
    },
    {
        "username": "Purple Deep",
        "message": "dasdas",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "6a53b162-3495-419d-b05b-0885a2f55462",
        "timestamp": "2023-07-28T15:28:03.719Z"
    },
    {
        "username": "Purple Deep",
        "message": "dasdas",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "cb26f352-b480-4594-b48b-91d354730076",
        "timestamp": "2023-07-28T15:28:05.968Z"
    },
    {
        "username": "Purple Deep",
        "message": "dasasd",
        "userId": "798f0692-5633-43ef-aea7-eb73fb638d41",
        "id": "a90f67bd-84b3-4493-97cc-6417382c7cd5",
        "timestamp": "2023-07-28T15:28:12.518Z"
    }
]

================
File: src/pages/api/audio-proxy.ts
================
import type { NextApiRequest, NextApiResponse } from "next";

interface CacheEntry {
  buffer: Buffer;
  timestamp: number;
  etag: string;
  contentType: string;
}

const CACHE_DURATION = 3600 * 1000 * 24 * 300; // 300 days;
const audioCache: Record<string, CacheEntry> = {};

export default async function handler(
  req: NextApiRequest,
  res: NextApiResponse,
) {
  if (req.method !== "GET") {
    res.setHeader("Allow", ["GET"]);
    res.status(405).end(`Method ${req.method} Not Allowed`);
    return;
  }

  const audioUrl = req.query.url as string;

  if (!audioUrl) {
    res.status(400).end("Audio URL is required");
    return;
  }

  try {
    const cachedAudio = audioCache[audioUrl];
    const ifNoneMatch = req.headers["if-none-match"];

    if (cachedAudio) {
      const isCacheValid = Date.now() - cachedAudio.timestamp < CACHE_DURATION;

      if (isCacheValid && ifNoneMatch === cachedAudio.etag) {
        res.status(304).end();
        return;
      }

      if (isCacheValid) {
        res.setHeader("ETag", cachedAudio.etag);
        res.setHeader(
          "Cache-Control",
          `public, max-age=${CACHE_DURATION}, stale-while-revalidate=86400`,
        );
        res.setHeader("Content-Type", cachedAudio.contentType);
        res.status(200).send(cachedAudio.buffer);
        return;
      }
    }

    const audioResponse = await fetch(audioUrl);

    if (!audioResponse.ok) {
      res.status(404).end("Audio not found");
      return;
    }

    const contentType =
      audioResponse.headers.get("content-type") || "audio/mpeg";
    const audioBuffer = Buffer.from(await audioResponse.arrayBuffer());
    const etag = `"${Buffer.from(audioBuffer).toString("base64").slice(0, 27)}"`;

    audioCache[audioUrl] = {
      buffer: audioBuffer,
      timestamp: Date.now(),
      etag,
      contentType,
    };

    res.setHeader("ETag", etag);
    res.setHeader(
      "Cache-Control",
      `public, max-age=${CACHE_DURATION}, stale-while-revalidate=86400`,
    );
    res.setHeader("Content-Type", contentType);
    res.setHeader("Vary", "Accept-Encoding");

    res.status(200).send(audioBuffer);
  } catch (error) {
    console.error("Error proxying audio:", error);
    res.status(500).end("Error fetching audio");
  }
}

================
File: src/pages/api/authenticate.ts
================
import { NextApiRequest, NextApiResponse } from "next";

export const handler = (req: NextApiRequest, res: NextApiResponse) => {
  const adminToken = req.query.admin_token;

  if (!adminToken) {
    return res.status(400).json({ error: "admin_token is required" });
  }

  const cookieValue = [
    `admin_token=${adminToken}`,
    "HttpOnly",
    process.env.NODE_ENV === "production" ? "Secure" : "",
    "SameSite=Strict",
    `Max-Age=${3600 * 24 * 365}`,
    "Path=/",
  ].join("; ");

  res.setHeader("Set-Cookie", cookieValue);
  res.status(200).json({ message: "Token set successfully" });
};

export default handler;

================
File: src/pages/api/og.tsx
================
import React from "react";
import { ImageResponse } from "@vercel/og";

export const config = {
  runtime: "edge",
};

export const OGImageResponse = () => {
  return new ImageResponse(Icon, {
    height: 500,
    width: 500,
  });
};

const Icon = (
  <svg xmlns="http://www.w3.org/2000/svg" width="500" height="497" viewBox="0 0 499 497">
    <g>
      <path fill="#F583FF" d="M303.98 171.336h116.598V420.59H303.98zm0 0"></path>
      <path fill="#B1BEFF" d="M187.379 171.336H303.98V420.59H187.38zm0 0"></path>
      <path fill="#EEFEC1" d="M70.777 171.336H187.38V420.59H70.777zm0 0"></path>
      <path
        fill="none"
        stroke="#2e3850"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="149.91"
        d="M1555.098 535.308c0-3.7-2.96-6.644-6.678-6.644H183.578c-3.635 0-6.623 2.985-6.623 6.616v1014.03c0 3.7 2.96 6.643 6.678 6.643h1364.842c3.635 0 6.623-2.985 6.623-6.616v-.027zm0 0"
        transform="scale(.28368 .284)"
      ></path>
      <path
        fill="none"
        stroke="#2e3850"
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth="149.91"
        d="M1210.566 186.22L866.033 528.665 521.501 186.221"
        transform="scale(.28368 .284)"
      ></path>
    </g>
  </svg>
);

// ORIGINAL STROKE COLOR - #3B4866

export default OGImageResponse;

================
File: src/pages/api/video-proxy.ts
================
import { NextApiRequest, NextApiResponse } from "next";

export const handler = async (req: NextApiRequest, res: NextApiResponse) => {
  const videoUrl = req.query.url as string;

  if (!videoUrl) {
    return res.status(400).json({ error: "url parameter is required." });
  }

  try {
    const videoResponse = await fetch(videoUrl, {
      method: "HEAD",
      mode: "no-cors",
      headers: {
        "Access-Control-Expose-Headers": "Content-Disposition",
      },
    });

    const contentDisposition = videoResponse.headers.get("Content-Disposition");

    const metadata = {
      url: videoUrl,
      title: contentDisposition?.split("filename=")?.[1]?.split(";")?.[0] ?? "Untitled",
      id: videoUrl,
      thumbnail: `/next-assets/images/synkro_placeholder.svg`,
      "content-type": videoResponse.headers.get("content-type"),
    };
    return res.status(200).json(metadata);
  } catch (error) {
    return res.status(500).json({ error: "Failed to fetch video." });
  }
};

export default handler;

================
File: src/pages/invite/[code]/index.tsx
================
import React, { useState } from "react";
import { GetServerSideProps } from "next";
import { useRouter } from "next/navigation";

import { useSocket } from "@/context/SocketContext";
import { useToast } from "@/components/ui/use-toast";

import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import DiceButton from "@/components/DiceButton";
import { EyeButton } from "@/components/EyeButton";
import { JOIN_ROOM_BY_INVITE } from "@/constants/socketActions";
import { fetcher } from "@/libs/utils/frontend-utils";
import { SERVER_URL } from "@/constants/constants";
import { Room } from "@/types/interfaces";

export interface InvitePageProps {
  sessionToken: string;
  code: string;
  hasPasscode: boolean;
}

export const InvitePage: React.FC<InvitePageProps> = ({ code, hasPasscode }) => {
  const router = useRouter();
  const [inviteCode, setInviteCode] = useState(code ?? "");
  const [username, setUsername] = useState("");
  const [roomPasscode, setRoomPasscode] = useState<string>("");
  const [isPasscodeHidden, setIsPasscodeHidden] = useState(true);

  const { socket } = useSocket();
  const { toast } = useToast();

  const handleChangeUsername = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUsername(value);
  };

  const handleChangeInviteCode = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setInviteCode(value);
  };

  const handleChangeRoomPasscode = (e: React.ChangeEvent<HTMLInputElement>) => {
    setRoomPasscode(e.target.value);
  };

  const handleGenerateRandomUsername = () => {
    import("@/libs/utils/names").then((module) => {
      setUsername(module.generateName());
    });
  };

  const handleTogglePasscodeHidden = () => setIsPasscodeHidden(!isPasscodeHidden);

  const handleJoinRoom = () => {
    if (!inviteCode.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Invite code is missing.",
      });
      return;
    } else if (!username.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Username is missing.",
      });
      return;
    } else if (hasPasscode && !roomPasscode.trim()) {
      toast({
        variant: "destructive",
        title: "Uh oh! Something went wrong",
        description: "Passcode is missing.",
      });
      return;
    }

    socket?.emit(JOIN_ROOM_BY_INVITE, inviteCode, username, roomPasscode, ({ success, roomId, error }) => {
      if (success) {
        router.push(`/room/${roomId}`);
      } else {
        toast({
          variant: "destructive",
          title: "Uh oh! Something went wrong",
          description: error || "Sorry, this room doesn't exist. 😥",
        });
      }
    });
  };

  return (
    <main className="flex flex-col pt-4">
      <div className="w-full h-full flex flex-col items-center justify-center px-2">
        <div className="max-w-[30rem] w-full bg-card p-4 rounded">
          <div className="flex">
            <Input placeholder="Invite code" onChange={handleChangeInviteCode} value={inviteCode} />
          </div>
          <div className="flex mt-3">
            <Input placeholder="Username" onChange={handleChangeUsername} value={username} />
            <DiceButton className="ml-2" onClick={handleGenerateRandomUsername} />
          </div>
          {hasPasscode && (
            <div className="flex mt-3">
              <Input
                type={isPasscodeHidden ? "password" : "text"}
                placeholder="Room Passcode"
                onChange={handleChangeRoomPasscode}
                value={roomPasscode}
              />
              <EyeButton active={isPasscodeHidden} className="ml-2" onClick={handleTogglePasscodeHidden} />
            </div>
          )}
          <div className="mt-4 flex flex-col items-center justify-end">
            <Button className="w-full h-9 py-1 px-2 border uppercase" onClick={handleJoinRoom} variant="default">
              Join Room
            </Button>
          </div>
        </div>
      </div>
    </main>
  );
};

export default InvitePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const sessionToken = context.req.cookies["session_token"] || null;
  const adminToken = context.req.cookies["admin_token"] || null;
  const code = context.params?.["code"] || null;

  const roomResponse = (await fetcher(`${SERVER_URL}/api/room-invite-code/${code}`, {
    headers: {
      "x-api-key": process.env.SERVER_API_KEY || "",
    },
  })) as { room: Room | null };

  const hasPasscode = !!roomResponse?.room?.passcode;

  return {
    props: {
      code,
      sessionToken,
      adminToken,
      hasPasscode,
    },
  };
};

================
File: src/pages/room/[id]/index.tsx
================
import React, { startTransition, useEffect, useRef, useState } from "react";
import { GetServerSideProps } from "next";
import Head from "next/head";
import { useRouter } from "next/navigation";

import ReactPlayer from "react-player";
import type ReactPlayerType from "react-player";
import { RefreshCcwIcon } from "lucide-react";

import {
  LEAVE_ROOM,
  USER_MESSAGE,
  SERVER_MESSAGE,
  PLAY_VIDEO,
  PAUSE_VIDEO,
  BUFFERING_VIDEO,
  RECONNECT_USER,
  FASTFORWARD_VIDEO,
  REWIND_VIDEO,
  CHANGE_VIDEO,
  SYNC_TIME,
  SYNC_VIDEO_INFORMATION,
  GET_VIDEO_INFORMATION,
  GET_HOST_VIDEO_INFORMATION,
  ADD_VIDEO_TO_QUEUE,
  END_OF_VIDEO,
  REMOVE_VIDEO_FROM_QUEUE,
  VIDEO_QUEUE_REORDERED,
  VIDEO_QUEUE_CLEARED,
  GET_ROOM_INFO,
  KICK_USER,
} from "../../../constants/socketActions";
import {
  ServerMessageType,
  type Messages,
  type VideoQueueItem,
} from "@/types/interfaces";

import Chat from "@/components/VideoRoom/Chat";
import Sidebar from "@/components/Sidebar";
import Queue from "@/components/VideoRoom/Queue";
import Settings from "@/components/VideoRoom/Settings";
import RoomToolbar, {
  ButtonActions,
  SidebarViews,
} from "@/components/VideoRoom/RoomToolbar";

import { AspectRatio } from "@/components/ui/aspect-ratio";
import { useToast } from "@/components/ui/use-toast";

import { useSocket } from "@/context/SocketContext";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { useQueue } from "@/hooks/useQueue";
import { useUpdateEffect } from "@/hooks/useUpdateEffect";

import { convertURLToCorrectProviderVideoId } from "@/libs/utils/frontend-utils";
import { Spinner } from "@/components/Spinner";
import {
  AUDIO_FILE_URL_REGEX,
  USER_DISCONNECTED_AUDIO,
  USER_JOINED_AUDIO,
  USER_KICKED_AUDIO,
  VIDEO_FILE_URL_REGEX,
} from "@/constants/constants";
import useAudio from "@/hooks/useAudio";

export interface RoomPageProps {
  sessionToken: string;
  deviceType: "desktop" | "mobile";
  roomId: string;
}

export const RoomPage: React.FC<RoomPageProps> = ({ sessionToken, roomId }) => {
  const [activeView, setActiveView] = useState<SidebarViews>("chat");
  const [isPlaying, setIsPlaying] = useState(false);
  //const [isMuted, setIsMuted] = useState(false);
  const [messages, setMessages] = useState<Messages>([]);
  const { socket, room, isConnecting } = useSocket();
  const [storedRoom, setStoredRoom] = useLocalStorage("room", room);
  const [currentVideoUrl, setCurrentVideoUrl] = useState<string>(
    room?.videoInfo.currentVideoUrl || "https://youtu.be/QdKhuEnkwiY",
  );
  const [isLoading, setIsLoading] = useState(false);
  const [isSyncing, setIsSyncing] = useState(false);

  const hostVideoInformationRef = useRef<{
    isPlaying?: boolean;
    videoUrl?: string;
  }>({});

  // TODO: MUTE VIDEO WHEN FIRST PLAYING

  const { play: playUserJoinedSound } = useAudio({
    volume: 0.1,
    src: USER_JOINED_AUDIO,
  });

  const { play: playUserDisconnectedSound } = useAudio({
    volume: 0.5,
    src: USER_DISCONNECTED_AUDIO,
  });

  const { play: playUserKickedSound } = useAudio({
    volume: 0.5,
    src: USER_KICKED_AUDIO,
  });

  const [player, setPlayer] = useState<ReactPlayerType | null>(null);

  const isSocketAvailable = !!socket;

  const router = useRouter();
  const { toast } = useToast();

  const videoQueue = useQueue<VideoQueueItem>();

  useEffect(() => {
    setStoredRoom(room);

    if (
      room &&
      Array.isArray(room?.videoInfo?.queue) &&
      room.videoInfo.queue.length > 0
    ) {
      videoQueue.set(room.videoInfo.queue);
    }

    if (room?.videoInfo.currentVideoUrl) {
      setCurrentVideoUrl(room?.videoInfo.currentVideoUrl);
    }
  }, [room]);

  const handleGoBackToHome = React.useCallback(() => {
    setStoredRoom(null);
    router.push("/");
  }, [storedRoom]);

  useEffect(() => {
    if (!isConnecting && socket?.connected && !room) {
      socket.emit(RECONNECT_USER, roomId, sessionToken, (result) => {
        if (!result.success) {
          console.error(result.error);
          toast({
            variant: "destructive",
            Icon: () => (
              <span>{result.error?.includes("authorized") ? "❌" : "😢"}</span>
            ),
            description: result.error,
            duration: 10000,
          });
          handleGoBackToHome();
        }
      });
    } else if (!room && storedRoom && !!socket) {
      socket.emit(RECONNECT_USER, roomId, sessionToken, (result) => {
        if (!result.success) {
          console.error(result.error);
          handleGoBackToHome();
        }
      });
    } else if (room === undefined && isConnecting === false) {
      handleGoBackToHome();
    }
  }, [isSocketAvailable, isConnecting, room]);

  useEffect(() => {
    if (room && room.members.length === 0) {
      handleGoBackToHome();
    }
  }, [room]);

  const socketMethods = React.useCallback(() => {
    if (!socket) return;

    socket.on(GET_ROOM_INFO, (newRoom) => {
      setStoredRoom(newRoom);
    });

    socket.on(SERVER_MESSAGE, (newMessage) => {
      if (
        [
          ServerMessageType.USER_JOINED,
          ServerMessageType.USER_RECONNECTED,
        ].includes(newMessage.type)
      ) {
        playUserJoinedSound();
      }
      if (newMessage.type === ServerMessageType.USER_DISCONNECTED) {
        playUserDisconnectedSound();
      }
      setMessages((prevMessages) => [...prevMessages, newMessage]);
    });

    socket.on(USER_MESSAGE, (newMessage) => {
      setMessages((prevMessages) => [...prevMessages, newMessage]);
    });

    socket.on(GET_HOST_VIDEO_INFORMATION, (callback) => {
      setIsSyncing(true);
      if (sessionToken !== room?.host) return;
      const currentVideoTime = player?.getCurrentTime() ?? 0;
      const currentVideoUrl = player?.props?.url as string;
      const isCurrentlyPlaying = player?.props?.playing as boolean;
      const currentDateTime = new Date().getTime();
      typeof callback === "function" &&
        callback(
          isCurrentlyPlaying,
          currentVideoUrl,
          currentVideoTime,
          currentDateTime,
        );
    });

    socket.on(PLAY_VIDEO, () => {
      hostVideoInformationRef.current = {
        isPlaying: true,
        videoUrl: hostVideoInformationRef.current.videoUrl || "",
      };
      setIsPlaying(true);
    });

    socket.on(PAUSE_VIDEO, () => {
      hostVideoInformationRef.current = {
        isPlaying: false,
        videoUrl: hostVideoInformationRef.current.videoUrl || "",
      };
      setIsPlaying(false);
    });

    socket.on(
      SYNC_VIDEO_INFORMATION,
      (playing, hostVideoUrl, elapsedVideoTime, eventCalledTime) => {
        setCurrentVideoUrl(hostVideoUrl);
        handleSyncTime(elapsedVideoTime, eventCalledTime, playing);
        setIsSyncing(false);
        hostVideoInformationRef.current = {
          isPlaying: playing,
          videoUrl: hostVideoUrl,
        };
      },
    );

    socket.on(REWIND_VIDEO, (newTime) => {
      player?.seekTo(newTime);
    });

    socket.on(FASTFORWARD_VIDEO, (newTime) => {
      player?.seekTo(newTime);
    });

    socket.on(CHANGE_VIDEO, (newVideoUrl) => {
      setCurrentVideoUrl(newVideoUrl);
      hostVideoInformationRef.current = {
        isPlaying: false,
        videoUrl: newVideoUrl || hostVideoInformationRef.current.videoUrl,
      };
      handleSyncTime(0, 0, true);
      handlePlay();
    });

    socket.on(ADD_VIDEO_TO_QUEUE, (newVideo) => {
      videoQueue.add(newVideo);
    });

    socket.on(REMOVE_VIDEO_FROM_QUEUE, (url) => {
      videoQueue.removeItem("url", url);
    });

    socket.on(VIDEO_QUEUE_REORDERED, (newVideoQueue) => {
      videoQueue.set(newVideoQueue);
    });

    socket.on(VIDEO_QUEUE_CLEARED, () => {
      videoQueue.set([]);
    });

    socket.on(SYNC_TIME, (currentVideoTime) => {
      handleSyncTime(
        currentVideoTime,
        0,
        hostVideoInformationRef.current.isPlaying || isPlaying,
      );
    });

    socket.on(KICK_USER, () => {
      socket.disconnect();
      setStoredRoom(null);
      playUserKickedSound();
      toast({
        variant: "destructive",
        Icon: () => <span>😢</span>,
        description: "You have been kicked",
        duration: 10000,
      });
      router.push("/");
    });

    socket.on(LEAVE_ROOM, () => {
      playUserDisconnectedSound();
      socket.disconnect();
      setStoredRoom(null);
      setActiveView("chat");
      router.push("/");
    });

    // elapsedVideoTime - seconds
    // eventCalledTime - milliseconds
    const handleSyncTime = (
      elapsedVideoTime: number,
      eventCalledTime: number,
      playing: boolean,
    ) => {
      if (!player) {
        console.error("Failed to sync time");
        return;
      }
      const currentVideoTime = player?.getCurrentTime() ?? 0;
      const userCurrentTime = new Date().getTime();
      const timeDifference = userCurrentTime - eventCalledTime;
      const eventCalledTimeInSeconds = timeDifference / 1000;

      if (
        currentVideoTime < elapsedVideoTime - 0.4 ||
        currentVideoTime > elapsedVideoTime + 0.4
      ) {
        player.seekTo(elapsedVideoTime + eventCalledTimeInSeconds, "seconds");
      }

      setIsPlaying(playing);
    };

    return () => {
      playUserDisconnectedSound();
      socket.offAnyOutgoing();
      socket.disconnect();
    };
  }, [socket, player, isSyncing, isPlaying]);

  const onReady = React.useCallback(
    (player: ReactPlayerType) => {
      if (
        sessionToken === room?.host ||
        sessionToken === process.env.NEXT_PUBLIC_ADMIN_TOKEN
      ) {
        if (
          !currentVideoUrl.match(VIDEO_FILE_URL_REGEX) &&
          !currentVideoUrl.match(AUDIO_FILE_URL_REGEX)
        ) {
          player?.seekTo(0, "seconds");
        }
        setIsPlaying(true);
        //setIsMuted(false);
      }
      setIsLoading(false);
      if (sessionToken !== room?.host) {
        socket?.emit(GET_VIDEO_INFORMATION);
      }
    },
    [currentVideoUrl, player, sessionToken, room?.host, socket],
  );

  useUpdateEffect(() => {
    if (!player && !isLoading) return;
    socketMethods();
  }, [player]);

  const handleLeaveRoom = () => {
    if (!socket) return;
    socket.emit(LEAVE_ROOM, roomId);
  };

  const runIfAuthorized = (
    callback?: () => void,
    disableAdminCheck = false,
  ) => {
    if (!socket) return;
    if (
      (socket?.data.isAdmin && !disableAdminCheck) ||
      room?.host === socket?.data?.userId
    ) {
      typeof callback === "function" && callback();
    }
  };

  const handlePlay = () => {
    setIsPlaying(true);
    runIfAuthorized(() => socket?.emit(PLAY_VIDEO));
  };

  const handlePause = () => {
    setIsPlaying(false);
    runIfAuthorized(() => socket?.emit(PAUSE_VIDEO));
  };

  const handleRewind = () => {
    if (!player) return null;
    const currentTime = player.getCurrentTime();
    const newTime = currentTime - 5 < 0 ? 0 : currentTime - 5;
    player.seekTo(newTime);
    runIfAuthorized(() => socket?.emit(REWIND_VIDEO, newTime));
  };

  const handleFastforward = () => {
    if (!player) return null;
    const currentTime = player.getCurrentTime();
    const endTime = player.getDuration();
    const newTime = currentTime + 5 > endTime ? endTime : currentTime + 5;
    player.seekTo(newTime);
    runIfAuthorized(() => socket?.emit(FASTFORWARD_VIDEO, newTime));
  };

  const handleBuffer = () => {
    if (!player) return null;
    const currentTime = player.getCurrentTime();
    if (socket?.data?.userId) {
      socket.emit(BUFFERING_VIDEO, currentTime);
    }
  };

  const handleEnded = () => {
    runIfAuthorized(() => socket?.emit(END_OF_VIDEO));
  };

  const handleClickPlayerButton = (
    buttonAction: ButtonActions,
    payload?: { videoUrl: string; videoIndex?: number },
  ) => {
    if (["chat", "queue", "settings"].includes(buttonAction)) {
      startTransition(() => {
        setActiveView(buttonAction as SidebarViews);
      });
    }

    switch (buttonAction) {
      case "play":
        handlePlay();
        return;
      case "pause":
        handlePause();
        return;
      case "rewind":
        handleRewind();
        return;
      case "fast-forward":
        handleFastforward();
        return;
      case "leave-room":
        handleLeaveRoom();
        return;
      case "change-video":
        if (
          typeof payload?.videoUrl === "string" &&
          ReactPlayer.canPlay(payload.videoUrl)
        ) {
          setCurrentVideoUrl(payload.videoUrl);
          socket?.emit(CHANGE_VIDEO, payload.videoUrl, payload.videoIndex);
          setIsPlaying(true);
        }
        return;
      case "sync-video":
        setIsSyncing(true);
        socket?.emit(GET_VIDEO_INFORMATION);
        toast({
          variant: "info",
          Icon: RefreshCcwIcon,
          iconClassname: "animate-spin",
          title: "Syncing with host",
          description: "This shouldn't take too long at all!",
          duration: 1000,
        });
        return;
      default:
        break;
    }
  };

  const currentVideoId = convertURLToCorrectProviderVideoId(
    currentVideoUrl,
  ) as string;

  const views: { [key in SidebarViews]: React.ReactNode } = {
    chat: <Chat messages={messages} roomId={roomId} />,
    queue: (
      <Queue
        currentVideoId={currentVideoId}
        videoQueue={videoQueue}
        onClickPlayerButton={handleClickPlayerButton}
      />
    ),
    settings: <Settings />,
  };

  // const onProgress = (state: OnProgressProps) => {
  //   // Might fallback to this if the videos consistently go out of sync
  //   //console.log("onProgress", state);
  // };

  if (!room) {
    return (
      <div className="w-full h-[calc(100vh-106px)] flex items-center justify-center">
        <Spinner />
      </div>
    );
  }

  return (
    <>
      <Head>
        <title>Synkro - {room.name ?? "Unknown"}</title>
      </Head>

      <main className="mx-auto h-full flex flex-col md:flex-row md:justify-center md:pt-4">
        <div className="flex flex-col max-w-[80rem] w-full">
          <div className="w-full">
            <div className="bg-card mb-2">
              <AspectRatio ratio={16 / 9}>
                <ReactPlayer
                  className="react-player"
                  url={currentVideoUrl}
                  width="100%"
                  height="100%"
                  playing={isPlaying}
                  onReady={onReady}
                  muted={false}
                  onBuffer={handleBuffer}
                  onPlay={handlePlay}
                  onPause={handlePause}
                  ref={setPlayer}
                  controls={true}
                  //onProgress={onProgress}
                  onEnded={handleEnded}
                  fallback={<div>LOADING</div>}
                />
              </AspectRatio>
            </div>
          </div>
          <div className="w-full flex items-center justify-center p-2 md:p-0">
            <RoomToolbar
              activeView={activeView}
              onClickPlayerButton={handleClickPlayerButton}
              isPlaying={isPlaying}
              roomId={roomId}
            />
          </div>
        </div>
        <Sidebar activeView={activeView} views={views} />
      </main>
    </>
  );
};

export default RoomPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const sessionToken = context.req.cookies["session_token"] || null;
  const adminToken = context.req.cookies["admin_token"] || null;
  const deviceType = context.req.cookies["device_type"] || null;

  const roomId = context.params?.["id"] || null;

  return {
    props: {
      sessionToken,
      adminToken,
      roomId,
      deviceType,
      navigationHeaderProps: {
        page: "video_room",
      },
    },
  };
};

================
File: src/pages/rooms/index.tsx
================
import { useMemo, useState } from "react";
import { GetServerSideProps } from "next";
import { useRouter } from "next/navigation";
import Link from "next/link";
import {
  ArrowDown01Icon,
  ArrowDown10Icon,
  ChevronRightIcon,
  HomeIcon,
  ServerIcon,
} from "lucide-react";

import { BUTTON_PRESS_AUDIO, SERVER_URL } from "@/constants/constants";
import { Room } from "@/types/interfaces";
import { cn } from "@/libs/utils/frontend-utils";

import { Input } from "@/components/ui/input";
import { Spinner } from "@/components/Spinner";
import { Button } from "@/components/ui/button";

import useAudio from "@/hooks/useAudio";
import useSSE from "@/hooks/useSSE";

interface RoomsPageProps {
  rooms: Room[];
}

export const RoomsPage: React.FC<RoomsPageProps> = ({
  rooms: initialRooms,
}) => {
  const [rooms, setRooms] = useState(initialRooms ?? []);
  const [sort, setSort] = useState<"asc" | "desc">("asc");
  const [loading, setLoading] = useState(true);
  const [isClosed, setIsClosed] = useState(false);
  const router = useRouter();

  const { play: playButtonClickSound } = useAudio({
    volume: 0.5,
    src: BUTTON_PRESS_AUDIO,
  });

  const options = useMemo(() => {
    return {
      onMessage: (event: MessageEvent) => {
        const data = JSON.parse(event.data) as { type: "room"; rooms: Room[] };
        if (data.type) {
          const newRooms = data.rooms ?? [];
          setRooms(newRooms);
        }
      },
      onOpen: () => {
        setLoading(false);
      },
      onError: (event: Event) => {
        console.error("AN ERROR OCCURED:", event);
        eventSource.close();
        if ((event?.target as EventSource)?.readyState === EventSource.CLOSED) {
          setIsClosed(true);
        }
      },
      onReconnect: () => {
        setLoading(true);
      },
    };
  }, []);

  const eventSource = useSSE(`${SERVER_URL}/api/public-rooms`, options);

  const handleNavigateToRoom = (room: Room) => {
    router.push(`/invite/${room.inviteCode}`);
  };

  const handleToggleSort = () => {
    playButtonClickSound();
    setSort((currentSort) => (currentSort === "asc" ? "desc" : "asc"));
  };

  const sortedRooms: Room[] = rooms
    .filter((room) => !room.private)
    .sort((roomA, roomB) => {
      if (sort === "asc") {
        return roomA.members.length - roomB.members.length;
      } else {
        return roomB.members.length - roomA.members.length;
      }
    });

  return (
    <main className="flex flex-col text-center relative h-auto px-2 pb-4">
      <div className="flex items-center bg-[#141428] max-w-[500px] w-full mx-auto mt-4 py-4 px-4 rounded-t">
        <div className="flex gap-x-1.5">
          <span className="block w-2.5 h-2.5 bg-[#f4645c] rounded-full"></span>
          <span className="block w-2.5 h-2.5 bg-[#f9be3e] rounded-full"></span>
          <span className="block w-2.5 h-2.5 bg-[#4ac645] rounded-full"></span>
        </div>
        <Input
          className="h-7 max-w-[200px] mx-2 ml-6 text-sm bg-[#070117] hover:border-input focus:border-input focus-visible:border-input"
          value="Room Browser"
          readOnly={true}
        />
        <button className="ml-auto" onClick={handleToggleSort}>
          {sort === "asc" ? (
            <ArrowDown01Icon color="#FFFFFF" size="1.25rem" />
          ) : (
            <ArrowDown10Icon color="#FFFFFF" size="1.25rem" />
          )}
        </button>
      </div>
      <div className="bg-card w-full h-full max-w-[500px] mx-auto p-4 rounded-b">
        <div className="flex flex-col w-full h-[500px] gap-y-4 overflow-y-auto overflow-x-hidden max-h-[calc(100vh-250px)] primary-scrollbar">
          {loading ? (
            <div className="w-full flex items-center justify-center">
              <Spinner />
            </div>
          ) : (
            <>
              {isClosed && (
                <div className="text-white text-sm">Connection closed</div>
              )}
              {!isClosed && sortedRooms.length === 0 && (
                <div className="text-white text-sm">
                  There are currently no public rooms available
                </div>
              )}
              {!isClosed &&
                sortedRooms.length > 0 &&
                sortedRooms.map((room) => (
                  <div
                    className={cn(
                      "w-full flex flex-col gap-2 sm:flex-row justify-between items-center flex-wrap bg-[#6b2ed73d] hover:bg-[#6b2ed766] text-foreground border border-primary box-border cursor-pointer p-2 rounded transition duration-300 ease-in-out",
                      {
                        "[bg-[#170d20]":
                          room.members.length === room.maxRoomSize,
                        "hover:bg-[#170d20]":
                          room.members.length === room.maxRoomSize,
                      },
                    )}
                    key={room.id}
                  >
                    <div className="flex flex-1 w-full">
                      <h2>{room.name}</h2>
                      <div className="flex items-center ml-auto">
                        <span className="mr-2">
                          {room.members.length} / {room.maxRoomSize}
                        </span>
                        <span>
                          <ServerIcon color="#FFFFFF" size="1.25rem" />
                        </span>
                      </div>
                    </div>
                    <Button
                      aria-label="Navigate to room"
                      onClick={() => handleNavigateToRoom(room)}
                      className="w-full sm:w-9 rounded-r"
                    >
                      <span>
                        <ChevronRightIcon color="#FFFFFF" size="1.25rem" />
                      </span>
                    </Button>
                  </div>
                ))}
            </>
          )}
        </div>
        <Link
          className="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors bg-secondary text-secondary-foreground shadow-sm hover:bg-secondary/80 w-full h-9 py-1 px-2 border uppercase mt-4"
          href="/"
        >
          Back Home
          <span className="ml-2">
            <HomeIcon color="#FFFFFF" size="1.25rem" />
          </span>
        </Link>
      </div>
    </main>
  );
};

export default RoomsPage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const sessionToken = context.req.cookies["session_token"] || null;
  const adminToken = context.req.cookies["admin_token"] || null;
  return {
    props: {
      sessionToken,
      adminToken,
      rooms: [],
    },
  };
};

================
File: src/pages/_app.tsx
================
import React from "react";
import type { AppProps } from "next/app";
import { Inter, Rubik } from "next/font/google";
import { Analytics } from "@vercel/analytics/react";
import { SpeedInsights } from "@vercel/speed-insights/next"

import { Page } from "@/components/Page";
import { Toaster } from "@/components/ui/toaster";
import { SocketProvider } from "@/context/SocketProvider";

import "@/styles/global.css";

const rubik = Rubik({ subsets: ["latin"], weight: ["300", "400", "500", "600", "700", "800", "900"] });
const inter = Inter({ weight: ["100", "200", "300", "400", "500", "600", "700"], subsets: ["latin"] });

export default function App({ Component, pageProps }: AppProps) {
  return (
    <>
      <SocketProvider sessionToken={pageProps?.sessionToken || null} adminToken={pageProps?.adminToken || null}>
        <div className={`${rubik.className} ${inter.className} h-screen flex flex-col`}>
          <Page sessionToken={pageProps?.sessionToken || null} navigationHeaderProps={pageProps.navigationHeaderProps}>
            <Component {...pageProps} />
            <Analytics />
            <SpeedInsights />
            <Toaster />
          </Page>
        </div>
      </SocketProvider>
    </>
  );
}

================
File: src/pages/_document.tsx
================
import { Html, Head, Main, NextScript } from "next/document";

export default function Document() {
  return (
    <Html lang="en">
      <Head></Head>
      <body className="dark">
        <Main />
        <NextScript />
      </body>
    </Html>
  );
}

================
File: src/pages/_error.tsx
================
import React, { useEffect } from "react";
import { NextPage } from "next";
import Link from "next/link";
import Image from "next/image";
import posthog from "posthog-js";

interface ErrorPageProps {
  statusCode?: number;
  errorMessage?: string | null;
}

const ErrorPage: NextPage<ErrorPageProps> = ({ statusCode, errorMessage }) => {
  useEffect(() => {
    const error = new Error(errorMessage ?? "Unknown error");
    posthog.captureException(error, { statusCode });
  }, [errorMessage]);

  console.error(errorMessage);
  return (
    <main className="flex flex-col h-full text-primary-foreground">
      <div className="w-full h-full flex flex-col items-center justify-center px-2 text-center">
        <h1 className="text-7xl font-bold mb-3">{statusCode}</h1>
        <p className="mb-3">Um. You shouldn&apos;t be here 😅</p>
        <Image
          alt=""
          src={"/next-assets/images/tv-stand-by.webp"}
          width={400}
          height={300}
          priority={true}
        />
        {errorMessage && <span>{errorMessage}</span>}
        <Link
          className="max-w-[12rem] w-full h-10 mt-4 bg-primary hover:bg-primary/90 transition-colors rounded flex items-center justify-center"
          href="/"
        >
          <span>Back to Home</span>
        </Link>
      </div>
    </main>
  );
};

ErrorPage.getInitialProps = async ({ res, err }) => {
  const statusCode = res ? res.statusCode : err ? err.statusCode : 404;
  return { statusCode, errorMessage: err?.message ?? null };
};

export default ErrorPage;

================
File: src/pages/404.tsx
================
import Image from "next/image";
import { NextPage } from "next";
import Link from "next/link";

const Error: NextPage = () => {
  return (
    <main className="flex flex-col h-full text-primary-foreground">
      <div className="w-full h-full flex flex-col items-center justify-center px-2 text-center">
        <h1 className="text-7xl font-bold mb-3">404</h1>
        <p className="mb-3">Um. You shouldn&apos;t be here 😅</p>
        <Image alt="" src={"/next-assets/images/tv-stand-by.webp"} width={400} height={300} priority={true} />
        <Link
          className="max-w-[12rem] w-full h-10 mt-4 bg-primary hover:bg-primary/90 transition-colors rounded flex items-center justify-center"
          href="/"
        >
          <span>Back to Home</span>
        </Link>
      </div>
    </main>
  );
};

export default Error;

================
File: src/pages/index.tsx
================
import { useState } from "react";
import { GetServerSideProps } from "next";
import CreateRoomBox from "@/components/CreateRoomBox";
import JoinRoomBox from "@/components/JoinRoomBox";

export const HomePage: React.FC<{ sessionToken: string | null }> = () => {
  const [isCreateBoxShown, setIsCreateBoxShown] = useState(true);

  const handleToggle = () => setIsCreateBoxShown(!isCreateBoxShown);

  return (
    <main className="flex flex-col pt-4">
      <div className="w-full h-full flex flex-col items-center justify-center px-2">
        {isCreateBoxShown ? <CreateRoomBox toggle={handleToggle} /> : <JoinRoomBox toggle={handleToggle} />}
      </div>
    </main>
  );
};

export default HomePage;

export const getServerSideProps: GetServerSideProps = async (context) => {
  const sessionToken = context.req.cookies["session_token"] || null;
  const adminToken = context.req.cookies["admin_token"] || null;
  return {
    props: {
      sessionToken,
      adminToken,
    },
  };
};

================
File: src/styles/global.css
================
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 224 71.4% 4.1%;
    --card: 0 0% 100%;
    --card-foreground: 224 71.4% 4.1%;
    --popover: 0 0% 100%;
    --popover-foreground: 224 71.4% 4.1%;
    --primary: 262.1 83.3% 57.8%;
    --primary-foreground: 210 20% 98%;
    --secondary: 220 14.3% 95.9%;
    --secondary-foreground: 220.9 39.3% 11%;
    --muted: 220 14.3% 95.9%;
    --muted-foreground: 220 8.9% 46.1%;
    --accent: 220 14.3% 95.9%;
    --accent-foreground: 220.9 39.3% 11%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 20% 98%;
    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 262.1 83.3% 57.8%;
    --radius: 0.3rem;
  }

  .dark {
    --background: 224 71.4% 4.1%;
    --foreground: 210 20% 98%;
    --card: 224 71.4% 4.1%;
    --card-foreground: 210 20% 98%;
    --popover: 224 71.4% 4.1%;
    --popover-foreground: 210 20% 98%;
    --primary: 263.4 70% 50.4%;
    --primary-foreground: 210 20% 98%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 210 20% 98%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 215 27.9% 16.9%;
    --accent-foreground: 210 20% 98%;
    --destructive: 0 72.65% 47.52%;
    --destructive-foreground: 210 20% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 263.4 70% 50.4%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

html {
  height: 100%;
}

body {
  background-color: #f1f6f9;
  color: #212a3e;
  height: 100%;
  font-family: "Rubik", sans-serif;
  background-image: url("/next-assets/images/background-gradient.png");
  background-position: 10px;
  background-position-x: center;
}

body.dark {
  background-color: #12111e;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.hide-scrollbar::-ms-overflow-style {
  display: none;
}

.hide-scrollbar::-webkit-scrollbar {
  width: 0;
  height: 0;
  background-color: transparent;
}

.primary-scrollbar {
  padding-right: 5px;
}

.primary-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.primary-scrollbar::-webkit-scrollbar-track {
  background-color: transparent;
  border-radius: 5px;
}

.primary-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 5px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: #5327a0;
}

.chat-scrollbar::-webkit-scrollbar {
  width: 10px;
}

.chat-scrollbar::-webkit-scrollbar-track {
  background-color: #0000006e;
  border-radius: 0px;
}

.chat-scrollbar::-webkit-scrollbar-thumb {
  border-radius: 8px;
  border: 2px solid transparent;
  background-clip: content-box;
  background-color: #bbb6d45d;
}

.input-colored {
  background: #3b2f51;
}

.star-field-canvas-wrapper * {
  color: #ffffff;
  background-color: #000;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.star-field-canvas-wrappercanvas {
  height: 100%;
  max-height: 100%;
}

================
File: src/types/interfaces.ts
================
export type UserId = string;
export type RoomId = string;

export interface User {
  id: UserId;
  socketId: string;
  username: string;
  roomId: RoomId;
  created: string;
  color: string;
  isAdmin?: boolean;
}

export interface Room {
  id: string;
  name: string;
  host: string;
  inviteCode: string | null;
  passcode: string | null;
  videoInfo: {
    currentVideoUrl: string | null;
    currentQueueIndex: number;
    queue: VideoQueueItem[];
  };
  members: User[];
  previouslyConnectedMembers: { userId: UserId; username: string }[];
  maxRoomSize: number;
  created: string;
  private: boolean;
}

export type Rooms = { [roomId: RoomId]: Room };

export interface ChatMessage {
  username: string;
  message: string;
  id: string;
  userId: UserId;
  timestamp: string;
  color: string;
  type: "USER";
  isAdmin: boolean;
}

export enum ServerMessageType {
  ALERT = "ALERT",
  DEFAULT = "DEFAULT",
  UPDATE = "UPDATE",
  NEW_HOST = "NEW_HOST",
  ERROR = "ERROR",
  USER_JOINED = "USER_JOINED",
  USER_DISCONNECTED = "USER_DISCONNECTED",
  USER_RECONNECTED = "USER_RECONNECTED",
}

export interface ServerMessage {
  message: string;
  type: ServerMessageType;
  timestamp: string;
  //id: string;
}

export type Message = ChatMessage | ServerMessage;
export type Messages = Message[];

export interface VideoQueueItem {
  id: string;
  title: string;
  url: string;
  thumbnail: string;
}

export enum VideoStatus {
  BUFFERING = "BUFFERING",
  PAUSED = "PAUSED",
}

================
File: src/types/socketCustomTypes.ts
================
import { Socket, Server } from "socket.io";
import {
  LEAVE_ROOM,
  USER_MESSAGE,
  SERVER_MESSAGE,
  JOIN_ROOM,
  CHECK_IF_ROOM_IS_FULL,
  CHECK_IF_ROOM_EXISTS,
  SET_HOST,
  GET_USERS,
  GET_ROOM_INFO,
  CREATE_ROOM,
  PLAY_VIDEO,
  PAUSE_VIDEO,
  BUFFERING_VIDEO,
  RECONNECT_USER,
  GET_USER_INFO,
  FASTFORWARD_VIDEO,
  REWIND_VIDEO,
  CHANGE_VIDEO,
  SYNC_TIME,
  SYNC_VIDEO_INFORMATION,
  GET_VIDEO_INFORMATION,
  GET_HOST_VIDEO_INFORMATION,
  ADD_VIDEO_TO_QUEUE,
  END_OF_VIDEO,
  REMOVE_VIDEO_FROM_QUEUE,
  VIDEO_QUEUE_REORDERED,
  CHANGE_SETTINGS,
  JOIN_ROOM_BY_INVITE,
  KICK_USER,
  SET_ADMIN,
  VIDEO_QUEUE_CLEARED,
  USER_VIDEO_STATUS,
} from "../constants/socketActions";
import { ChatMessage, Room, ServerMessage, User, VideoQueueItem, VideoStatus } from "./interfaces";

export interface ClientToServerEvents {
  connect: () => void;
  disconnect: () => void;
  [JOIN_ROOM]: (
    roomId: string,
    username: string,
    callback: (value: { success: boolean; error?: string }) => void
  ) => void;
  [LEAVE_ROOM]: (roomId: string) => void;
  [RECONNECT_USER]: (
    roomId: string,
    userId: string | null,
    callback: (value: { success: boolean; error?: string }) => void
  ) => void;
  [USER_MESSAGE]: (message: string, roomId: string) => void;
  [SERVER_MESSAGE]: ({ message, type }: ServerMessage) => void;
  [CHECK_IF_ROOM_EXISTS]: (roomId: string, callback: (room: Room | null) => void) => void;
  [CREATE_ROOM]: (
    username: string,
    roomName: string,
    callback: (value: { result?: Room; error?: string }) => void
  ) => void;
  [SET_HOST]: (host: string) => void;
  [KICK_USER]: (userId: string) => void;
  [GET_USERS]: (users: User[]) => void;
  [GET_ROOM_INFO]: (roomId: string, callback: (room: Room) => void) => void;
  [PLAY_VIDEO]: () => void;
  [PAUSE_VIDEO]: () => void;
  [FASTFORWARD_VIDEO]: (newTime: number) => void;
  [REWIND_VIDEO]: (newTime: number) => void;
  [BUFFERING_VIDEO]: (time: number) => void;
  [CHANGE_VIDEO]: (newVideoUrl: string, newIndex?: number) => void;
  [END_OF_VIDEO]: () => void;
  [SYNC_TIME]: (currentVideoTime: number) => void;
  [SYNC_VIDEO_INFORMATION]: (
    callback: (playing: boolean, hostVideoUrl: string, elapsedVideoTime: number, eventCalledTime: number) => void
  ) => void;
  [GET_HOST_VIDEO_INFORMATION]: (
    callback: (playing: boolean, hostVideoUrl: string, elapsedVideoTime: number, eventCalledTime: number) => void
  ) => void;
  [GET_VIDEO_INFORMATION]: () => void;
  [ADD_VIDEO_TO_QUEUE]: (video: VideoQueueItem) => void;
  [REMOVE_VIDEO_FROM_QUEUE]: (url: string) => void;
  [VIDEO_QUEUE_REORDERED]: (videoQueue: VideoQueueItem[]) => void;
  [VIDEO_QUEUE_CLEARED]: () => void;
  [CHANGE_SETTINGS]: (newSettings: { maxRoomSize: number; roomPasscode: string | null; private: boolean }) => void;
  [JOIN_ROOM_BY_INVITE]: (
    inviteCode: string,
    username: string,
    roomPasscode: string,
    callback: (value: { success: boolean; roomId?: string; error?: string }) => void
  ) => void;
  [USER_VIDEO_STATUS]: (userId: string, videoStatus: VideoStatus) => void;
}

export interface ServerToClientEvents {
  connect: () => void;
  disconnect: () => void;
  connect_error: (error: any) => void;
  [JOIN_ROOM]: (
    roomId: string,
    username: string,
    callback: (value: { success: boolean; error: string }) => void
  ) => void;
  [LEAVE_ROOM]: () => void;
  [KICK_USER]: () => void;
  [SET_ADMIN]: () => void;
  [SERVER_MESSAGE]: ({ message, type }: ServerMessage) => void;
  [USER_MESSAGE]: (message: ChatMessage) => void;
  [CHECK_IF_ROOM_IS_FULL]: (roomId: string, callback: any) => void;
  [CHECK_IF_ROOM_EXISTS]: (roomId: string, callback: (room: Room | null) => void) => void;
  [CREATE_ROOM]: (
    username: string,
    roomName: string,
    callback: (value: { result?: Room; error?: string }) => void
  ) => void;
  [SET_HOST]: (host: string) => void;
  [GET_ROOM_INFO]: (room: Room) => void;
  [GET_USER_INFO]: (user: User) => void;
  [PLAY_VIDEO]: () => void;
  [PAUSE_VIDEO]: () => void;
  [FASTFORWARD_VIDEO]: (newTime: number) => void;
  [REWIND_VIDEO]: (newTime: number) => void;
  [CHANGE_VIDEO]: (newVideoUrl: string, newIndex?: number) => void;
  [SYNC_TIME]: (currentVideoTime: number) => void;
  [BUFFERING_VIDEO]: (time: number) => void;
  [SYNC_VIDEO_INFORMATION]: (
    playing: boolean,
    hostVideoUrl: string,
    elapsedVideoTime: number,
    eventCalledTime: number
  ) => void;
  [GET_HOST_VIDEO_INFORMATION]: (
    callback: (playing: boolean, hostVideoUrl: string, elapsedVideoTime: number, eventCalledTime: number) => void
  ) => void;
  [GET_VIDEO_INFORMATION]: () => void;
  [ADD_VIDEO_TO_QUEUE]: (video: VideoQueueItem) => void;
  [REMOVE_VIDEO_FROM_QUEUE]: (url: string) => void;
  [VIDEO_QUEUE_REORDERED]: (videoQueue: VideoQueueItem[]) => void;
  [VIDEO_QUEUE_CLEARED]: () => void;
  [JOIN_ROOM_BY_INVITE]: (
    inviteCode: string,
    username: string,
    roomPasscode: string,
    callback: (value: { success: boolean; roomId?: string; error?: string }) => void
  ) => void;
  [USER_VIDEO_STATUS]: (userId: string, videoStatus: VideoStatus) => void;
}

export interface InterServerEvents {
  ping: () => void;
}

export interface SocketData {
  userId: string | undefined;
  roomId: string | undefined;
  isAdmin: boolean | undefined;
}

export type CustomSocket = Socket<ServerToClientEvents, ClientToServerEvents, InterServerEvents, SocketData>;
export type CustomServer = Server<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;
export type CustomServerSocket = Socket<ClientToServerEvents, ServerToClientEvents, InterServerEvents, SocketData>;

================
File: src/instrumentation.ts
================
import { type Instrumentation } from "next";
import posthog from "posthog-js";

export const onRequestError: Instrumentation.onRequestError = async (
  error,
  _request,
  _context,
) => {
  posthog.captureException(error as Error);
};

================
File: src/middleware.ts
================
import { NextRequest, NextResponse, userAgent } from "next/server";
import { v4 as uuidv4 } from "uuid";
//import arcjet, { createMiddleware, shield } from "@arcjet/next";

export const config = {
  // matcher tells Next.js which routes to run the middleware on.
  // This runs the middleware on all routes except for static assets.
  /*
   * - _next/static (static files)
   * - _next/image (image optimization files)
   * - favicon.ico (favicon file)
   */
  matcher: ["/((?!_next/static|_next/image|favicon.ico|next-assets).*)"],
};

// const aj = arcjet({
//   key: process.env.ARCJET_KEY!,
//   rules: [
//     // Protect against common attacks with Arcjet Shield
//     shield({
//       mode: "LIVE", // will block requests. Use "DRY_RUN" to log only
//     }),
//   ],
// });

const middleware = async (req: NextRequest): Promise<NextResponse> => {
  const { device } = userAgent(req);

  const res = NextResponse.next();

  const deviceType = device.type === "mobile" ? "mobile" : "desktop";
  res.cookies.set("device_type", deviceType, {
    httpOnly: true,
    secure: process.env.NODE_ENV === "production",
    sameSite: "strict",
    maxAge: 3600 * 24 * 365,
    path: "/",
  });

  let sessionToken = req.cookies.get("session_token")?.value;

  if (!sessionToken) {
    sessionToken = uuidv4();
    res.cookies.set("session_token", sessionToken, {
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 3600 * 24 * 365,
      path: "/",
    });
    return res;
  }

  return res;
};

// export default process.env.NODE_ENV === "production"
//   ? createMiddleware(aj, middleware)
//   : middleware;

export default middleware;

================
File: .eslintrc.json
================
{
  "extends": "next/core-web-vitals",
  "endOfLine":"auto"
}

================
File: .gitignore
================
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

.vercel
.env

.env*.local
.vercel

# Million Lint
.million
server/src/utils/mockRoom.ts
.vscode/settings.json

================
File: .vercelignore
================
/server

================
File: components.json
================
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "new-york",
  "rsc": false,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.js",
    "css": "src/styles/global.css",
    "baseColor": "slate",
    "cssVariables": true
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}

================
File: Dockerfile
================
FROM node:18.16.0-alpine as base

# Set the working directory to the server folder
WORKDIR /server

# Add package file
COPY /server/package.json ./

# Install deps
RUN yarn install

# Copy source
COPY /server/src ./src
COPY /server/types ./types
COPY /server/constants ./constants
COPY tsconfig.json ./

# Build dist
RUN yarn build

# Start production image build
FROM node:18.16.0-alpine

# Set the working directory to the server folder
WORKDIR /server

# Copy node modules and build directory
COPY --from=base /server/node_modules ./node_modules
COPY --from=base /server/dist ./dist

# Expose port 8000
EXPOSE 8000

# Start the application
CMD ["node", "dist/app.js"]

================
File: eslint.config.mjs
================
import path from "node:path";
import { fileURLToPath } from "node:url";
import js from "@eslint/js";
import { FlatCompat } from "@eslint/eslintrc";

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const compat = new FlatCompat({
    baseDirectory: __dirname,
    recommendedConfig: js.configs.recommended,
    allConfig: js.configs.all
});
export default [...compat.extends("next/core-web-vitals")];

================
File: next.config.ts
================
import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  reactStrictMode: true,
  async headers() {
    return [
      {
        source:
          "/ingest/static/(web-vitals.js|dead-clicks-autocapture.js|recorder.js)",
        headers: [
          {
            key: "Cache-Control",
            value: "public, max-age=31536000, immutable",
          },
          {
            key: "X-Content-Type-Options",
            value: "nosniff",
          },
        ],
      },
    ];
  },
  async rewrites() {
    return [
      {
        source: "/ingest/static/:path*",
        destination: "https://us-assets.i.posthog.com/static/:path*",
      },
      {
        source: "/ingest/:path*",
        destination: "https://us.i.posthog.com/:path*",
      },
    ];
  },
  images: {
    dangerouslyAllowSVG: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "www.synkro.live",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "i.ytimg.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "i1.sndcdn.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "i.vimeocdn.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "embed-ssl.wistia.com",
        pathname: "**",
      },
      {
        protocol: "https",
        hostname: "static-cdn.jtvnw.net",
        pathname: "**",
      },
    ],
  },
};

export default nextConfig;

================
File: package.json
================
{
  "name": "synkro",
  "version": "0.1.0",
  "private": true,
  "scripts": {
    "dev": "concurrently \"nodemon ./server/src/app.ts\" \"next dev -p 3000\"",
    "build": "next build",
    "start": "next start -p 3000",
    "lint": "next lint",
    "prepare": "husky"
  },
  "lint-staged": {
    "*.{js,jsx,ts,tsx}": [
      "eslint --fix",
      "prettier --write"
    ]
  },
  "dependencies": {
    "@arcjet/next": "1.0.0-beta.1",
    "@hello-pangea/dnd": "^17.0.0",
    "@million/lint": "1.0.13",
    "@next/bundle-analyzer": "^15.2.4",
    "@radix-ui/react-aspect-ratio": "^1.1.0",
    "@radix-ui/react-dialog": "^1.1.2",
    "@radix-ui/react-icons": "^1.3.2",
    "@radix-ui/react-label": "^2.1.0",
    "@radix-ui/react-slot": "^1.1.0",
    "@radix-ui/react-switch": "^1.1.1",
    "@radix-ui/react-toast": "^1.2.2",
    "@radix-ui/react-tooltip": "^1.1.8",
    "@types/node": "22.10.1",
    "@types/react": "19.0.0",
    "@types/react-beautiful-dnd": "^13.1.8",
    "@types/react-dom": "19.0.0",
    "@types/screenfull": "^4.1.0",
    "@types/ua-parser-js": "^0.7.39",
    "@types/uuid": "^10.0.0",
    "@types/ws": "^8.5.13",
    "@vercel/analytics": "^1.4.1",
    "@vercel/og": "^0.6.4",
    "@vercel/speed-insights": "^1.1.0",
    "add": "^2.0.6",
    "autoprefixer": "10.4.20",
    "body-parser": "^1.20.3",
    "class-variance-authority": "^0.7.1",
    "clsx": "^2.1.1",
    "cors": "^2.8.5",
    "eslint": "9.16.0",
    "eslint-config-next": "15.2.4",
    "lucide-react": "0.468.0",
    "million": "^3.1.11",
    "mysql": "^2.18.1",
    "nanoid": "5.0.9",
    "next": "^15.2.4",
    "next-qrcode": "^2.5.1",
    "postcss": "8.4.49",
    "posthog-js": "^1.211.1",
    "react": "19.0.0",
    "react-dom": "19.0.0",
    "react-player": "^2.16.0",
    "scheduler": "^0.25.0",
    "socket.io": "^4.8.1",
    "socket.io-client": "^4.8.1",
    "tailwind-merge": "^2.5.5",
    "tailwindcss": "3.4.16",
    "tailwindcss-animate": "^1.0.7",
    "typescript": "5.7.2",
    "ua-parser-js": "^2.0.0",
    "use-context-selector": "^2.0.0",
    "uuid": "^11.0.3"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3.2.0",
    "@eslint/js": "^9.16.0",
    "concurrently": "^9.1.0",
    "husky": "^9.1.7",
    "lint-staged": "^15.2.10",
    "prettier": "^3.4.2",
    "ts-node": "^10.9.2"
  }
}

================
File: postcss.config.js
================
module.exports = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}

================
File: README.md
================
# This is the client code for the Synkro Web App

Syknro is an app built to watch videos with friends in sync!

## Getting Started

First, install the needed dependencies:

```bash
npm install
# or
yarn install
# or
pnpm install
```

Next, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

================
File: tailwind.config.js
================
/** @type {import('tailwindcss').Config} */
module.exports = {
  darkMode: ["class"],
  content: ["./pages/**/*.{ts,tsx}", "./components/**/*.{ts,tsx}", "./app/**/*.{ts,tsx}", "./src/**/*.{ts,tsx}"],
  theme: {
    container: {
      center: true,
      padding: "2rem",
    },
    fontFamily: {
      inter: ["Inter", "system-ui"],
      sans: ["ui-sans-serif", "system-ui"],
    },
    extend: {
      colors: {
        // "brand-white": "#F1F6F9",
        // "brand-blue-600": "#394867",
        // "brand-blue-800": "#212A3E",
        // "brand-purple-100": "#E5D0FB",
        // "brand-purple-200": "#D7BBF5",
        // "brand-indigo-200": "#a684f7",
        // "brand-indigo-300": "#9288F8",
        // "brand-indigo-400": "#826cb7",
        // "brand-indigo-800": "#6528F7",
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: 0 },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: 0 },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
    screens: {
      sm: "640px",
      md: "880px",
      lg: "1024px",
      xl: "1280px",
      "2xl": "1400px",
    },
  },
  plugins: [require("tailwindcss-animate")],
};

================
File: tsconfig.json
================
{
  "compilerOptions": {
    "target": "es5",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "forceConsistentCasingInFileNames": true,
    "noEmit": false,
    "noImplicitAny": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "node",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "paths": {
      "@/*": ["./src/*"],
      "@backend/*": ["../server/src/*"],
      "@shared/*": ["../shared/*"],
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx"],
  "exclude": ["node_modules"]
}




================================================================
End of Codebase
================================================================
