import { createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { nanoid } from 'nanoid';
import { roomsSource } from './lib/utils/room-management';
import { generateQuestion } from './lib/utils/gameLogic';
import { Player, GameRoom } from './lib/types/multiplayer';
import {
  CREATE_ROOM,
  JOIN_ROOM,
  LEAVE_ROOM,
  START_GAME,
  SUBMIT_ANSWER,
  NEXT_QUESTION,
  GET_ROOM_INFO,
  USER_JOINED,
  USER_LEFT,
  GAME_STATE_CHANGED,
  PLAYER_SCORE_UPDATED,
  QUESTION_REVEALED,
  ROUND_ENDED,
  ERROR,
  CHECK_IF_ROOM_EXISTS
} from './lib/constants/socketActions';

interface ExtendedWebSocket extends WebSocket {
  playerId?: string;
  roomId?: string;
  sessionToken?: string;
}

const server = createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ status: 'WebSocket server running' }));
});

const wss = new WebSocketServer({ server });

// Helper functions
function generatePlayerColor(): string {
  const colors = [
    '#ef4444', '#f97316', '#eab308', '#22c55e', '#06b6d4',
    '#3b82f6', '#6366f1', '#8b5cf6', '#ec4899', '#f59e0b'
  ];
  return colors[Math.floor(Math.random() * colors.length)];
}

function broadcastToRoom(roomId: string, message: any, excludePlayerId?: string) {
  wss.clients.forEach((client: ExtendedWebSocket) => {
    if (client.roomId === roomId &&
        client.readyState === WebSocket.OPEN &&
        client.playerId !== excludePlayerId) {
      client.send(JSON.stringify(message));
    }
  });
}

function sendToPlayer(playerId: string, message: any) {
  wss.clients.forEach((client: ExtendedWebSocket) => {
    if (client.playerId === playerId && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify(message));
    }
  });
}

function serializeRoom(room: GameRoom) {
  return {
    ...room,
    players: Array.from(room.players.entries()).map(([id, player]) => [id, player])
  };
}

wss.on('connection', (ws: ExtendedWebSocket) => {
  console.log('New WebSocket connection');

  ws.on('message', async (data) => {
    try {
      const message = JSON.parse(data.toString());
      console.log('Received message:', message.type);

      switch (message.type) {
        case 'init':
          ws.sessionToken = message.sessionToken;
          ws.send(JSON.stringify({ type: 'init_success' }));
          break;

        case CREATE_ROOM:
          handleCreateRoom(ws, message);
          break;

        case JOIN_ROOM:
          handleJoinRoom(ws, message);
          break;

        case LEAVE_ROOM:
          handleLeaveRoom(ws);
          break;

        case START_GAME:
          handleStartGame(ws);
          break;

        case SUBMIT_ANSWER:
          handleSubmitAnswer(ws, message);
          break;

        case NEXT_QUESTION:
          handleNextQuestion(ws);
          break;

        case CHECK_IF_ROOM_EXISTS:
          handleCheckRoomExists(ws, message);
          break;

        default:
          ws.send(JSON.stringify({
            type: ERROR,
            message: `Unknown message type: ${message.type}`
          }));
      }
    } catch (error) {
      console.error('Error processing message:', error);
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Invalid message format'
      }));
    }
  });

  ws.on('close', () => {
    console.log('WebSocket connection closed');
    if (ws.playerId && ws.roomId) {
      handlePlayerDisconnect(ws);
    }
  });

  ws.on('error', (error) => {
    console.error('WebSocket error:', error);
  });
});

function handleCreateRoom(ws: ExtendedWebSocket, message: any) {
  try {
    const { name, username, difficulty = 'medium', maxPlayers = 4 } = message;

    if (!name || !username) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room name and username are required'
      }));
      return;
    }

    const roomId = nanoid(6).toUpperCase();
    const playerId = nanoid();

    const player: Player = {
      id: playerId,
      name: username,
      score: 0,
      isHost: true,
      isAdmin: true,
      socketId: playerId,
      roomId,
      connectedAt: new Date().toISOString(),
      color: generatePlayerColor(),
      hasAnswered: false
    };

    const room = roomsSource.create(roomId, name, player);
    room.difficulty = difficulty;
    room.maxPlayers = maxPlayers;

    ws.playerId = playerId;
    ws.roomId = roomId;

    ws.send(JSON.stringify({
      type: GET_ROOM_INFO,
      room: serializeRoom(room)
    }));

    console.log(`Room created: ${roomId} by ${username}`);
  } catch (error) {
    console.error('Error creating room:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to create room'
    }));
  }
}

function handleJoinRoom(ws: ExtendedWebSocket, message: any) {
  try {
    const { roomId, username } = message;

    if (!roomId || !username) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room ID and username are required'
      }));
      return;
    }

    const room = roomsSource.get(roomId);
    if (!room) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room not found'
      }));
      return;
    }

    if (room.players.size >= room.maxPlayers) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room is full'
      }));
      return;
    }

    const playerId = nanoid();
    const player: Player = {
      id: playerId,
      name: username,
      score: 0,
      isHost: false,
      isAdmin: false,
      socketId: playerId,
      roomId,
      connectedAt: new Date().toISOString(),
      color: generatePlayerColor(),
      hasAnswered: false
    };

    roomsSource.addPlayer(roomId, player);

    ws.playerId = playerId;
    ws.roomId = roomId;

    // Send room info to the new player
    ws.send(JSON.stringify({
      type: GET_ROOM_INFO,
      room: serializeRoom(room)
    }));

    // Notify other players
    broadcastToRoom(roomId, {
      type: USER_JOINED,
      player,
      room: serializeRoom(room)
    }, playerId);

    console.log(`Player ${username} joined room ${roomId}`);
  } catch (error) {
    console.error('Error joining room:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to join room'
    }));
  }
}

function handleLeaveRoom(ws: ExtendedWebSocket) {
  try {
    if (!ws.playerId || !ws.roomId) return;

    const room = roomsSource.get(ws.roomId);
    if (!room) return;

    const player = room.players.get(ws.playerId);
    if (!player) return;

    roomsSource.removePlayer(ws.roomId, ws.playerId);

    // Notify other players
    broadcastToRoom(ws.roomId, {
      type: USER_LEFT,
      playerId: ws.playerId,
      room: serializeRoom(roomsSource.get(ws.roomId)!)
    });

    // If room is empty, delete it
    if (room.players.size === 0) {
      roomsSource.delete(ws.roomId);
    }

    ws.playerId = undefined;
    ws.roomId = undefined;

    console.log(`Player ${player.name} left room`);
  } catch (error) {
    console.error('Error leaving room:', error);
  }
}

function handleStartGame(ws: ExtendedWebSocket) {
  try {
    if (!ws.playerId || !ws.roomId) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Not in a room'
      }));
      return;
    }

    const room = roomsSource.get(ws.roomId);
    if (!room) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room not found'
      }));
      return;
    }

    const player = room.players.get(ws.playerId);
    if (!player?.isHost) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Only the host can start the game'
      }));
      return;
    }

    if (room.players.size < 2) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Need at least 2 players to start'
      }));
      return;
    }

    const success = roomsSource.startGame(ws.roomId);
    if (!success) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Failed to start game'
      }));
      return;
    }

    // Broadcast game start to all players
    broadcastToRoom(ws.roomId, {
      type: GAME_STATE_CHANGED,
      gameState: 'playing',
      room: serializeRoom(room)
    });

    broadcastToRoom(ws.roomId, {
      type: QUESTION_REVEALED,
      question: room.currentQuestion
    });

    console.log(`Game started in room ${ws.roomId}`);
  } catch (error) {
    console.error('Error starting game:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to start game'
    }));
  }
}

function handleSubmitAnswer(ws: ExtendedWebSocket, message: any) {
  try {
    if (!ws.playerId || !ws.roomId) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Not in a room'
      }));
      return;
    }

    const { answer, isCorrect } = message;
    const room = roomsSource.get(ws.roomId);
    if (!room || room.gameState !== 'playing') {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Game not in progress'
      }));
      return;
    }

    const player = room.players.get(ws.playerId);
    if (!player || player.hasAnswered) {
      return; // Already answered
    }

    const success = roomsSource.submitAnswer(ws.roomId, ws.playerId, isCorrect);
    if (success) {
      // Broadcast score update
      broadcastToRoom(ws.roomId, {
        type: PLAYER_SCORE_UPDATED,
        playerId: ws.playerId,
        score: player.score,
        hasAnswered: true
      });

      console.log(`Player ${player.name} answered ${isCorrect ? 'correctly' : 'incorrectly'}`);
    }
  } catch (error) {
    console.error('Error submitting answer:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to submit answer'
    }));
  }
}

function handleNextQuestion(ws: ExtendedWebSocket) {
  try {
    if (!ws.playerId || !ws.roomId) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Not in a room'
      }));
      return;
    }

    const room = roomsSource.get(ws.roomId);
    if (!room) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Room not found'
      }));
      return;
    }

    const player = room.players.get(ws.playerId);
    if (!player?.isHost) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Only the host can advance to next question'
      }));
      return;
    }

    const success = roomsSource.nextQuestion(ws.roomId);
    if (!success) {
      ws.send(JSON.stringify({
        type: ERROR,
        message: 'Failed to advance to next question'
      }));
      return;
    }

    const updatedRoom = roomsSource.get(ws.roomId)!;

    if (updatedRoom.gameState === 'finished') {
      // Game finished
      broadcastToRoom(ws.roomId, {
        type: GAME_STATE_CHANGED,
        gameState: 'finished',
        room: serializeRoom(updatedRoom)
      });
    } else {
      // Next question
      broadcastToRoom(ws.roomId, {
        type: QUESTION_REVEALED,
        question: updatedRoom.currentQuestion,
        round: updatedRoom.currentRound
      });
    }

    console.log(`Advanced to next question in room ${ws.roomId}`);
  } catch (error) {
    console.error('Error advancing to next question:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to advance to next question'
    }));
  }
}

function handleCheckRoomExists(ws: ExtendedWebSocket, message: any) {
  try {
    const { roomId } = message;
    const exists = roomsSource.has(roomId);

    ws.send(JSON.stringify({
      type: 'room_exists_response',
      roomId,
      exists
    }));
  } catch (error) {
    console.error('Error checking room existence:', error);
    ws.send(JSON.stringify({
      type: ERROR,
      message: 'Failed to check room existence'
    }));
  }
}

function handlePlayerDisconnect(ws: ExtendedWebSocket) {
  try {
    if (!ws.playerId || !ws.roomId) return;

    const room = roomsSource.get(ws.roomId);
    if (!room) return;

    const player = room.players.get(ws.playerId);
    if (!player) return;

    // For now, remove the player immediately
    // In a production app, you might want to keep them for a grace period
    roomsSource.removePlayer(ws.roomId, ws.playerId);

    // Notify other players
    broadcastToRoom(ws.roomId, {
      type: USER_LEFT,
      playerId: ws.playerId,
      playerName: player.name,
      room: serializeRoom(roomsSource.get(ws.roomId)!)
    });

    // If room is empty, delete it
    const updatedRoom = roomsSource.get(ws.roomId);
    if (!updatedRoom || updatedRoom.players.size === 0) {
      roomsSource.delete(ws.roomId);
      console.log(`Room ${ws.roomId} deleted (empty)`);
    }

    console.log(`Player ${player.name} disconnected from room ${ws.roomId}`);
  } catch (error) {
    console.error('Error handling player disconnect:', error);
  }
}

const PORT = process.env.WS_PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server listening on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}`);
});