import { createServer } from 'http';
import { WebSocketServer, WebSocket } from 'ws';
import { nanoid } from 'nanoid';
import { roomsSource, Player, GameRoom } from './lib/utils/room-management';
import {
  CREATE_ROOM,
  JOIN_ROOM,
  LEAVE_ROOM,
  CHECK_IF_ROOM_EXISTS,
  GET_ROOM_INFO,
  START_GAME,
  SUBMIT_ANSWER,
  NEXT_QUESTION,
  GAME_FINISHED,
  USER_JOINED,
  USER_LEFT,
  SET_HOST,
  KICK_USER,
  PING,
  PONG,
  RECONNECT_USER,
  ERROR,
  SERVER_MESSAGE
} from './lib/constants/socketActions';

// Game state management
const players = new Map<string, Player>();
const socketToPlayer = new Map<WebSocket, string>();
const playerToSocket = new Map<string, WebSocket>();
const sessionTokenToPlayer = new Map<string, string>();
const pendingSockets = new Map<WebSocket, { 
  sessionToken: string | null;
  messageQueue: any[];
  isInitialized: boolean;
}>();

// Create HTTP server for health checks
const server = createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify({ 
    status: 'ok', 
    players: players.size, 
    rooms: roomsSource.getLength(),
    timestamp: new Date().toISOString()
  }));
});

// Create WebSocket server
const wss = new WebSocketServer({ server });

function generatePlayerId(): string {
  return nanoid(12);
}

function broadcastToRoom(roomId: string, event: string, data: any) {
  const room = roomsSource.get(roomId);
  if (!room) return;

  const message = JSON.stringify({ type: event, ...data });
  room.players.forEach(player => {
    const socket = playerToSocket.get(player.id);
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(message);
    }
  });
}

function handleMessage(socket: WebSocket, data: any) {
  try {
    const playerId = socketToPlayer.get(socket);
    if (!playerId) {
      socket.send(JSON.stringify({ type: ERROR, message: 'Player not found' }));
      return;
    }

    const player = players.get(playerId);
    if (!player) {
      socket.send(JSON.stringify({ type: ERROR, message: 'Player not found' }));
      return;
    }

    console.log(`Message from ${playerId}:`, data.type);

    switch (data.type) {
      case CREATE_ROOM:
        handleCreateRoom(socket, player, data);
        break;
      case JOIN_ROOM:
        handleJoinRoom(socket, player, data);
        break;
      case LEAVE_ROOM:
        handleLeaveRoom(socket, player);
        break;
      case CHECK_IF_ROOM_EXISTS:
        handleCheckRoomExists(socket, data);
        break;
      case START_GAME:
        handleStartGame(socket, player);
        break;
      case SUBMIT_ANSWER:
        handleSubmitAnswer(socket, player, data);
        break;
      case NEXT_QUESTION:
        handleNextQuestion(socket, player);
        break;
      case PING:
        socket.send(JSON.stringify({ type: PONG }));
        break;
      default:
        console.log('Unknown message type:', data.type);
        socket.send(JSON.stringify({ type: ERROR, message: 'Unknown message type' }));
    }
  } catch (error) {
    console.error('Error handling message:', error);
    socket.send(JSON.stringify({ type: ERROR, message: 'Internal server error' }));
  }
}

function handleCreateRoom(socket: WebSocket, player: Player, data: any) {
  if (!data.name || typeof data.name !== 'string') {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room name is required' }));
    return;
  }

  if (data.name.length > 80) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room name cannot exceed 80 characters' }));
    return;
  }

  const roomId = nanoid(6);
  const roomName = data.name.trim();
  const maxPlayers = data.maxPlayers || 4;
  const difficulty = data.difficulty || 'medium';

  // Update player info
  player.name = data.username || player.name;
  player.isHost = true;
  player.roomId = roomId;

  // Create room
  const room = roomsSource.create(roomId, roomName, player);
  room.maxPlayers = maxPlayers;
  room.difficulty = difficulty as any;
  roomsSource.set(roomId, room);

  console.log(`Room ${roomId} created by player ${player.id} (${player.name})`);

  socket.send(JSON.stringify({
    type: GET_ROOM_INFO,
    room: {
      id: room.id,
      name: room.name,
      host: room.host,
      players: Array.from(room.players.values()).map(p => ({ 
        id: p.id, 
        name: p.name, 
        score: p.score,
        isHost: p.isHost 
      })),
      gameState: room.gameState,
      maxPlayers: room.maxPlayers,
      difficulty: room.difficulty,
      currentRound: room.currentRound,
      totalRounds: room.totalRounds,
      inviteCode: room.inviteCode
    }
  }));
}

function handleJoinRoom(socket: WebSocket, player: Player, data: any) {
  if (!data.roomId || !data.username) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room ID and username are required' }));
    return;
  }

  if (typeof data.username !== 'string' || data.username.length > 80) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Invalid username' }));
    return;
  }

  const room = roomsSource.get(data.roomId);
  if (!room) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room not found' }));
    return;
  }

  if (room.players.size >= room.maxPlayers) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room is full' }));
    return;
  }

  // Update player info
  player.name = data.username;
  player.roomId = data.roomId;
  player.isHost = false;

  // Add player to room
  const success = roomsSource.addPlayer(data.roomId, player);
  if (!success) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Failed to join room' }));
    return;
  }

  console.log(`Player ${player.id} (${player.name}) joined room ${data.roomId}`);

  // Send room info to joining player
  socket.send(JSON.stringify({
    type: GET_ROOM_INFO,
    room: {
      id: room.id,
      name: room.name,
      host: room.host,
      players: Array.from(room.players.values()).map(p => ({ 
        id: p.id, 
        name: p.name, 
        score: p.score,
        isHost: p.isHost 
      })),
      gameState: room.gameState,
      maxPlayers: room.maxPlayers,
      difficulty: room.difficulty,
      currentRound: room.currentRound,
      totalRounds: room.totalRounds,
      inviteCode: room.inviteCode
    }
  }));

  // Broadcast to other players in room
  broadcastToRoom(data.roomId, USER_JOINED, {
    player: { id: player.id, name: player.name, score: player.score, isHost: player.isHost }
  });
}

function handleLeaveRoom(socket: WebSocket, player: Player) {
  if (!player.roomId) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Not in a room' }));
    return;
  }

  const success = roomsSource.removePlayer(player.roomId, player.id);
  if (success) {
    // Broadcast to other players
    broadcastToRoom(player.roomId, USER_LEFT, { playerId: player.id });
    
    player.roomId = '';
    player.isHost = false;
    console.log(`Player ${player.id} left room ${player.roomId}`);
  }

  socket.send(JSON.stringify({ type: 'left_room', success }));
}

function handleCheckRoomExists(socket: WebSocket, data: any) {
  if (!data.roomId) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Room ID is required' }));
    return;
  }

  const exists = roomsSource.has(data.roomId);
  socket.send(JSON.stringify({ type: 'room_exists', exists }));
}

function handleStartGame(socket: WebSocket, player: Player) {
  if (!player.roomId) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Not in a room' }));
    return;
  }

  if (!player.isHost) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Only host can start the game' }));
    return;
  }

  const success = roomsSource.startGame(player.roomId);
  if (success) {
    const room = roomsSource.get(player.roomId);
    if (room) {
      const roomInfo = {
        id: room.id,
        name: room.name,
        host: room.host,
        players: Array.from(room.players.values()).map(p => ({ 
          id: p.id, 
          name: p.name, 
          score: p.score,
          isHost: p.isHost 
        })),
        gameState: room.gameState,
        maxPlayers: room.maxPlayers,
        difficulty: room.difficulty,
        currentRound: room.currentRound,
        totalRounds: room.totalRounds,
        currentQuestion: room.currentQuestion
      };

      // Broadcast to all players in room
      broadcastToRoom(player.roomId, GET_ROOM_INFO, { room: roomInfo });
    }
  } else {
    socket.send(JSON.stringify({ type: ERROR, message: 'Failed to start game' }));
  }
}

function handleSubmitAnswer(socket: WebSocket, player: Player, data: any) {
  if (!player.roomId) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Not in a room' }));
    return;
  }

  const success = roomsSource.submitAnswer(player.roomId, player.id, data.isCorrect);
  if (success) {
    const room = roomsSource.get(player.roomId);
    if (room) {
      const updatedPlayer = room.players.get(player.id);
      socket.send(JSON.stringify({
        type: 'answer_result',
        isCorrect: data.isCorrect,
        score: updatedPlayer?.score || 0,
        correctAnswer: room.currentQuestion?.currentCountry?.name
      }));
    }
  }
}

function handleNextQuestion(socket: WebSocket, player: Player) {
  if (!player.roomId) {
    socket.send(JSON.stringify({ type: ERROR, message: 'Not in a room' }));
    return;
  }

  const success = roomsSource.nextQuestion(player.roomId);
  if (success) {
    const room = roomsSource.get(player.roomId);
    if (room) {
      if (room.gameState === 'finished') {
        const finalResults = {
          players: Array.from(room.players.values()).map(p => ({ 
            id: p.id, 
            name: p.name, 
            score: p.score 
          }))
        };
        broadcastToRoom(player.roomId, GAME_FINISHED, finalResults);
      } else {
        const roomInfo = {
          id: room.id,
          name: room.name,
          host: room.host,
          players: Array.from(room.players.values()).map(p => ({ 
            id: p.id, 
            name: p.name, 
            score: p.score,
            isHost: p.isHost 
          })),
          gameState: room.gameState,
          maxPlayers: room.maxPlayers,
          difficulty: room.difficulty,
          currentRound: room.currentRound,
          totalRounds: room.totalRounds,
          currentQuestion: room.currentQuestion
        };
        broadcastToRoom(player.roomId, GET_ROOM_INFO, { room: roomInfo });
      }
    }
  }
}

function handlePlayerDisconnect(playerId: string) {
  const player = players.get(playerId);
  if (!player) return;

  if (player.roomId) {
    roomsSource.removePlayer(player.roomId, playerId);
    // Broadcast to other players
    broadcastToRoom(player.roomId, USER_LEFT, { playerId });
  }

  players.delete(playerId);
  playerToSocket.delete(playerId);
  
  // Remove session token mapping
  for (const [token, pid] of sessionTokenToPlayer.entries()) {
    if (pid === playerId) {
      sessionTokenToPlayer.delete(token);
      break;
    }
  }
  
  //console.log(`Player disconnected: ${playerId}`);
}

// WebSocket connection handling
wss.on('connection', (ws: WebSocket) => {
  console.log('New WebSocket connection established');
  
  // Store pending socket until we get the session token
  pendingSockets.set(ws, { 
    sessionToken: null,
    messageQueue: [],
    isInitialized: false
  });

  ws.on('message', (message: Buffer) => {
    try {
      const data = JSON.parse(message.toString());
      const pending = pendingSockets.get(ws);
      
      if (!pending) {
        console.log('Socket not found in pending sockets');
        return;
      }
      
      // Check if this is the first message with session token
      if (data.type === 'init' && data.sessionToken && !pending.isInitialized) {
        const sessionToken = data.sessionToken;
        pending.sessionToken = sessionToken;
        pending.isInitialized = true;
        
        let playerId: string;
        let player: Player;

        // Check if we already have a player with this session token
        if (sessionTokenToPlayer.has(sessionToken)) {
          playerId = sessionTokenToPlayer.get(sessionToken)!;
          player = players.get(playerId)!;
          
          // Update the socket reference for this existing player
          const oldSocket = playerToSocket.get(playerId);
          if (oldSocket && oldSocket !== ws) {
            console.log(`Closing old socket for player ${playerId}`);
            oldSocket.close();
          }
          
          playerToSocket.set(playerId, ws);
          socketToPlayer.set(ws, playerId);
          
          console.log(`Player reconnected with session token: ${sessionToken} (${playerId})`);
          
          // Send current room info if player was in a room
          if (player.roomId) {
            const room = roomsSource.get(player.roomId);
            if (room) {
              ws.send(JSON.stringify({
                type: GET_ROOM_INFO,
                room: {
                  id: room.id,
                  name: room.name,
                  host: room.host,
                  players: Array.from(room.players.values()).map(p => ({ 
                    id: p.id, 
                    name: p.name, 
                    score: p.score,
                    isHost: p.isHost 
                  })),
                  gameState: room.gameState,
                  maxPlayers: room.maxPlayers,
                  difficulty: room.difficulty,
                  currentRound: room.currentRound,
                  totalRounds: room.totalRounds,
                  inviteCode: room.inviteCode
                }
              }));
            }
          }
        } else {
          // Create new player
          playerId = generatePlayerId();
          console.log(`New player connected: ${playerId} (session token: ${sessionToken})`);

          player = {
            id: playerId,
            name: '',
            score: 0,
            isHost: false,
            isAdmin: false,
            socketId: '',
            roomId: '',
            connectedAt: new Date().toISOString()
          };

          players.set(playerId, player);
          socketToPlayer.set(ws, playerId);
          playerToSocket.set(playerId, ws);
          
          // Store session token mapping
          sessionTokenToPlayer.set(sessionToken, playerId);
        }
        
        // Process any queued messages
        if (pending.messageQueue.length > 0) {
          console.log(`Processing ${pending.messageQueue.length} queued messages for player ${playerId}`);
          pending.messageQueue.forEach(queuedData => {
            handleMessage(ws, queuedData);
          });
        }
        pending.messageQueue = [];
        
        // Remove from pending sockets
        pendingSockets.delete(ws);
        
        // Send confirmation
        ws.send(JSON.stringify({ type: 'init_success' }));
        return;
      }
      
      // If not initialized yet, queue the message
      if (!pending.isInitialized) {
        console.log('Queueing message before initialization:', data.type);
        pending.messageQueue.push(data);
        return;
      }
      
      // Handle other messages normally
      handleMessage(ws, data);
    } catch (error) {
      console.error('Error parsing message:', error);
      ws.send(JSON.stringify({ 
        type: ERROR, 
        message: 'Invalid message format' 
      }));
    }
  });

  ws.on('close', () => {
    const playerId = socketToPlayer.get(ws);
    if (playerId) {
      console.log(`Player ${playerId} disconnected`);
      handlePlayerDisconnect(playerId);
      socketToPlayer.delete(ws);
    } else {
      console.log('Pending socket disconnected before initialization');
    }
    pendingSockets.delete(ws);
  });

  ws.on('error', (error) => {
    const playerId = socketToPlayer.get(ws);
    if (playerId) {
      console.error(`WebSocket error for player ${playerId}:`, error);
      handlePlayerDisconnect(playerId);
      socketToPlayer.delete(ws);
    } else {
      console.error('WebSocket error for pending socket:', error);
    }
    pendingSockets.delete(ws);
  });
});

// Cleanup empty rooms periodically
setInterval(() => {
  const rooms = roomsSource.getAllAsArray();
  rooms.forEach(room => {
    if (room.players.size === 0) {
      roomsSource.delete(room.id);
      console.log(`Cleaned up empty room: ${room.id}`);
    }
  });
}, 60000); // Clean up every minute

// Start server
const PORT = process.env.WS_PORT || 3001;
server.listen(PORT, () => {
  console.log(`WebSocket server listening on port ${PORT}`);
  console.log(`Health check available at http://localhost:${PORT}`);
}); 